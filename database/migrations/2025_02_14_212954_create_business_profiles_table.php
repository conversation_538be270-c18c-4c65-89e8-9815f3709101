<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_profiles', function (Blueprint $table) {
            $table->id();
            $table->string('taxid');
            $table->string('official_name');
            $table->string('date_of_issue')->nullable();
            $table->string('date_of_tax_closure')->nullable();
            $table->string('trading_name')->nullable();
            $table->string('tax_management_office')->nullable();
            $table->string('head_office_address')->nullable();
            $table->string('tax_registration_office')->nullable();
            $table->string('tax_notification_address')->nullable();
            $table->string('decision_date')->nullable();
            $table->string('business_license')->nullable();
            $table->string('issue_date')->nullable();
            $table->string('issuing_authority')->nullable();
            $table->string('declaration_receipt_date')->nullable();
            $table->string('fiscal_year_start')->nullable();
            $table->string('fiscal_year_end')->nullable();
            $table->string('current_taxid')->nullable();
            $table->string('start_date')->nullable();
            $table->string('program_section')->nullable();
            $table->string('accounting_method')->nullable();
            $table->string('vat_calculation_method')->nullable();
            $table->string('legal_representative')->nullable();
            $table->string('legal_representative_address')->nullable();
            $table->string('legal_representative_id')->nullable();
            $table->string('director_name')->nullable();
            $table->string('director_address')->nullable();
            $table->string('chief_accountant')->nullable();
            $table->string('note')->nullable();
            $table->json('data')->nullable();
            $table->text('industry')->nullable();
            $table->json('dvtructhuoc')->nullable();
            $table->json('vpdd')->nullable();
            $table->json('dvthanhvien')->nullable();
            $table->json('tax_type')->nullable();
            $table->json('dnchuquan')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_profiles');
    }
};
