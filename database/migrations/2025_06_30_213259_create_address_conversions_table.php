<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('address_conversions', function (Blueprint $table) {
            $table->id();
            $table->string('ward_name');
            $table->string('district_name');
            $table->string('province_name');
            $table->text('detail_address');
            $table->text('new_detail');
            $table->string('new_ward');
            $table->string('new_ward_code');
            $table->string('new_province');
            $table->string('new_province_code');
            $table->text('new_full_address');
            $table->boolean('not_sure')->default(false);
            $table->boolean('success')->default(false);
            $table->boolean('ward_code_missing')->default(false);
            $table->boolean('province_code_missing')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('address_conversions');
    }
};
