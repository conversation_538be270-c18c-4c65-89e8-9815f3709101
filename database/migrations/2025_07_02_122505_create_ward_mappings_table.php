<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ward_mappings', function (Blueprint $table) {
            $table->id();
            $table->string('old_ward_code')->nullable();
            $table->string('old_ward_name')->nullable();
            $table->string('old_district_name')->nullable();
            $table->string('old_province_name')->nullable();
            $table->string('new_ward_code')->nullable();
            $table->string('new_ward_name')->nullable();
            $table->string('new_province_name')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ward_mappings');
    }
};
