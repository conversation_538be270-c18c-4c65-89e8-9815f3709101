<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tax_businesses', function (Blueprint $table) {
            $table->string('director_name')->nullable()->after('representative_name');
            $table->string('chief_accountant')->nullable()->after('director_name');
        });
    }

    public function down(): void
    {
        Schema::table('tax_businesses', function (Blueprint $table) {
            $table->dropColumn(['director_name', 'chief_accountant']);
        });
    }
};
