<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tax_businesses', function (Blueprint $table) {
            $table->id();
            $table->string('tax_id')->unique();
            $table->string('name');
            $table->string('alternate_name')->nullable();
            $table->string('short_alternate_name')->nullable();
            $table->string('address');
            $table->string('ward_code');
            $table->string('representative_name');
            $table->string('phone')->nullable();
            $table->date('establish_date')->nullable();
            $table->string('management_agency')->nullable();
            $table->string('company_type')->nullable();
            $table->string('primary_industry')->nullable();
            $table->string('industries')->nullable();
            $table->string('status')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tax_businesses');
    }
};
