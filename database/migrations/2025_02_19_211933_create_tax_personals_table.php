<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tax_personals', function (Blueprint $table) {
            $table->id();
            $table->string('tax_id')->unique();
            $table->string('name');
            $table->string('id_number');
            $table->string('address');
            $table->string('phone')->nullable();
            $table->date('issue_date')->nullable();
            $table->date('close_date')->nullable();
            $table->string('tax_management_office')->nullable();
            $table->string('head_office_address')->nullable();
            $table->string('status')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tax_personals');
    }
};
