<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('tax_businesses', function (Blueprint $table) {
            $table->text('address')->nullable()->change(); // Modify the column
        });
    }

    public function down(): void
    {
        Schema::table('tax_businesses', function (Blueprint $table) {
            $table->string('address')->nullable()->change(); // Revert the change
        });
    }
};
