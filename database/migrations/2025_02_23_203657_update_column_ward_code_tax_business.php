<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tax_businesses', function (Blueprint $table) {
            $table->string('ward_code')->nullable()->change();
        });
    }

    public function down(): void
    {
        Schema::table('tax_businesses', function (Blueprint $table) {
            $table->string('ward_code')->nullable(false)->change();
        });
    }
};
