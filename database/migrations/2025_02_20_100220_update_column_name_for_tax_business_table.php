<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tax_businesses', function (Blueprint $table) {
            $table->text('name')->change(); // Modify the column
            $table->text('alternate_name')->change(); // Modify the column
        });
    }

    public function down(): void
    {
        Schema::table('tax_businesses', function (Blueprint $table) {
            $table->string('name')->change(); // Revert the change
            $table->string('alternate_name')->change(); // Revert the change
        });
    }
};
