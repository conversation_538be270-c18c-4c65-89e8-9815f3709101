{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "2captcha/2captcha": "^1.2", "artesaos/seotools": "^1.3", "botman/botman": "^2.8", "botman/driver-facebook": "^1.11", "botman/driver-telegram": "^2.0", "filament/filament": "^3.2", "intervention/image": "^3.11", "irazasyed/telegram-bot-sdk": "^3.15", "laravel/framework": "^11.31", "laravel/horizon": "^5.30", "laravel/octane": "^2.7", "laravel/scout": "^10.13", "laravel/tinker": "^2.9", "livewire/flux": "^2.0", "livewire/flux-pro": "^2.0", "matchish/laravel-scout-elasticsearch": "^7.11", "openai-php/client": "^0.10.3", "spatie/laravel-sitemap": "^7.3"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.14", "fakerphp/faker": "^1.23", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "pestphp/pest": "^3.7", "pestphp/pest-plugin-laravel": "^3.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/helper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": {"flux-pro": {"type": "composer", "url": "https://composer.fluxui.dev"}}}