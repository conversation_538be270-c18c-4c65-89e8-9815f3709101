<?php

return [
    'host' => env('ELASTICSEARCH_HOST'),
    'user' => env('ELASTICSEARCH_USER'),
    'password' => env('ELASTICSEARCH_PASSWORD'),
    'cloud_id' => env('<PERSON>LA<PERSON><PERSON><PERSON>AR<PERSON>_CLOUD_ID'),
    'api_key' => env('ELASTICSEARCH_API_KEY'),
    'ssl_verification' => env('ELASTICSEARCH_SSL_VERIFICATION', true),
    'queue' => [
        'timeout' => env('SCOUT_QUEUE_TIMEOUT'),
    ],
    'indices' => [
        'mappings' => [
            'default' => [
                'properties' => [
                    'id' => [
                        'type' => 'keyword',
                    ],
                ],
            ],
            'tax_businesses' => [
                'properties' => [
                    "name" => [
                        "type" => "text",
                        "analyzer" => "vietnamese_analyzer"
                    ],
                    "industries" => [
                        "type" => "text",
                        "analyzer" => "comma_analyzer"
                    ]
                ],
            ]
        ],
        'settings' => [
            'default' => [
                'number_of_shards' => 1,
                'number_of_replicas' => 0,
            ],
            'tax_businesses' => [
                "analysis" => [
                    "analyzer" => [
                        "vietnamese_analyzer" => [
                            "tokenizer" => "standard",
                            "filter" => [
                                "asciifolding",
                                "lowercase"
                            ]
                        ],
                        "comma_analyzer" => [
                            "type" => "custom",
                            "tokenizer" => "pattern",
                            "pattern" => ","
                        ]
                    ]
                ]
            ]
        ],
    ],
];
