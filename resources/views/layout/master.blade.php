<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">


    {{-- @if(request()->getQueryString())
        <meta name="robots" content="noindex, nofollow">
    @endif --}}


    {!! SEOMeta::generate() !!}
    {!! JsonLd::generate() !!}
    <link rel="icon" type="image/x-icon" href="{{ asset('images/logo/favicon.png') }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=Nunito" rel="stylesheet" />

    <!-- Styles / Scripts -->
    @if (file_exists(public_path('build/manifest.json')) || file_exists(public_path('hot')))
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @endif
    @fluxAppearance
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-MGGK3GFQ');</script>
    <!-- End Google Tag Manager -->
</head>

<body class="min-h-screen bg-white dark:bg-zinc-800">
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MGGK3GFQ"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <flux:header container class="bg-zinc-50 dark:bg-zinc-900 border-b border-zinc-200 dark:border-zinc-700">
        <flux:sidebar.toggle class="lg:hidden" icon="bars-2" inset="left" />

        <div class="mr-3">
            <a href="{{ route('home') }}">
                <img class="w-30 max-lg:hidden dark:hidden" src="{{ asset('images/logo/thongtin_light.png') }}" alt="">
                <img class="w-30 max-lg:!hidden hidden dark:flex" src="{{ asset('images/logo/thongtin_dark.png') }}"
                    alt="">
            </a>
        </div>

        <flux:navbar class="-mb-px max-lg:hidden">
            <flux:navbar.item icon="user" href="{{ route('search-tax-personal') }}"
                :current="request()->route()->getName() == 'search-tax-personal'">Tra cứu mã số thuế cá nhân
            </flux:navbar.item>
            <flux:navbar.item icon="briefcase" href="{{ route('industries') }}"
                :current="request()->route()->getName() == 'industries'">Ngành nghề</flux:navbar.item>
            <flux:navbar.item icon="truck" href="{{ route('phat-nguoi') }}"
                :current="request()->route()->getName() == 'phat-nguoi'" badge="Mới" badge-color="lime">Phạt nguội
            </flux:navbar.item>
            <livewire:quick-search>
                {{-- <flux:navbar.item icon="book-open" href="#">Danh bạ website</flux:navbar.item> --}}
        </flux:navbar>

        <flux:spacer />

        <flux:navbar class="mr-4">
            <flux:button size="sm" x-data x-on:click="$flux.dark = ! $flux.dark" icon="moon" variant="subtle"
                aria-label="Toggle dark mode" />
        </flux:navbar>

        <flux:navbar class="-mb-px">
            <flux:navbar.item href="#">Đăng nhập</flux:navbar.item>
            <flux:navbar.item href="#">Đăng ký</flux:navbar.item>
        </flux:navbar>
    </flux:header>

    <flux:sidebar stashable sticky
        class="lg:hidden bg-zinc-50 dark:bg-zinc-900 border-r border-zinc-200 dark:border-zinc-700">
        <flux:sidebar.toggle class="lg:hidden" icon="x-mark" />

        <div class="">
            <a href="{{ route('home') }}">
                <img class="w-30 px-2 dark:hidden" src="{{ asset('images/logo/thongtin_light.png') }}" alt="">
                <img class="w-30 px-2 hidden dark:flex" src="{{ asset('images/logo/thongtin_dark.png') }}" alt="">
            </a>
        </div>
        {{--
        <flux:brand href="{{ route('home') }}" logo="https://fluxui.dev/img/demo/logo.png" name="dev.thongtin.vn"
            class="px-2 dark:hidden" />
        <flux:brand href="{{ route('home') }}" logo="https://fluxui.dev/img/demo/dark-mode-logo.png"
            name="dev.thongtin.vn" class="px-2 hidden dark:flex" /> --}}

        <flux:navlist variant="outline">
            <flux:navbar.item icon="user" href="{{ route('search-tax-personal') }}" :current="false">Tra cứu mã số thuế
                cá nhân
            </flux:navbar.item>
            <flux:navbar.item icon="briefcase" href="{{ route('industries') }}" :current="false">Ngành nghề
            </flux:navbar.item>
            <flux:navbar.item icon="truck" href="{{ route('phat-nguoi') }}" :current="false">Phạt nguội
            </flux:navbar.item>
            <livewire:quick-search :position='"sidebar"'>
                {{-- <flux:navbar.item icon="book-open" href="#" :current="false">Danh bạ website
                </flux:navbar.item> --}}
        </flux:navlist>

        <flux:spacer />

        <flux:navlist variant="outline">
            <flux:navlist.item icon="cog-6-tooth" href="#">Cài đặt</flux:navlist.item>
            <flux:navlist.item icon="information-circle" href="#">Hỗ trợ</flux:navlist.item>
        </flux:navlist>
    </flux:sidebar>

    {{-- <flux:main container>
        @yield('content')
    </flux:main> --}}

    <flux:main container>

        <div class="grid sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-2 items-center mt-2 mb-5 items-stretch gap-2">
            <div>
                <livewire:components.search-tax>
            </div>
            <div>
                <div
                    class="relative overflow-hidden border border-sky-200 dark:border-sky-300/30 bg-sky-50/50 dark:bg-sky-400/15 rounded-lg items-stretch h-full p-6">
                    <div class="opacity-10 dark:opacity-20 absolute right-0 top-0 -translate-y-[45%] translate-x-[25%]">
                        <flux:icon.check-badge class="size-6 text-sky-500 dark:text-black size-[10rem]" />
                    </div>

                    <div class="[--color-accent:var(--color-sky-600)] [--color-accent-content:var(--color-sky-600)] [--color-accent-foreground:var(--color-white)] dark:[--color-accent:var(--color-sky-600)] dark:[--color-accent-content:var(--color-sky-400)] dark:[--color-accent-foreground:var(--color-white)]
                        flex items-center justify-center gap-3 h-full">
                        <div class="font-medium text-sm text-accent-content dark:text-sky-100">Tham gia cộng đồng</div>
                        <a class="inline-block text-sky-900 dark:text-white" href="#" target="_blank"
                            rel="noopener noreferrer">
                            <svg class="size-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path fill-rule="evenodd"
                                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                                    clip-rule="evenodd" />
                            </svg>
                        </a>
                        <a class="inline-block text-sky-500 dark:text-white" href="https://t.me/thongtinvn_group"
                            target="_blank" rel="noopener noreferrer">
                            <svg class="size-6" fill="currentColor" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px"
                                width="100" height="100" viewBox="0 0 50 50">
                                <path
                                    d="M25,2c12.703,0,23,10.297,23,23S37.703,48,25,48S2,37.703,2,25S12.297,2,25,2z M32.934,34.375	c0.423-1.298,2.405-14.234,2.65-16.783c0.074-0.772-0.17-1.285-0.648-1.514c-0.578-0.278-1.434-0.139-2.427,0.219	c-1.362,0.491-18.774,7.884-19.78,8.312c-0.954,0.405-1.856,0.847-1.856,1.487c0,0.45,0.267,0.703,1.003,0.966	c0.766,0.273,2.695,0.858,3.834,1.172c1.097,0.303,2.346,0.04,3.046-0.395c0.742-0.461,9.305-6.191,9.92-6.693	c0.614-0.502,1.104,0.141,0.602,0.644c-0.502,0.502-6.38,6.207-7.155,6.997c-0.941,0.959-0.273,1.953,0.358,2.351	c0.721,0.454,5.906,3.932,6.687,4.49c0.781,0.558,1.573,0.811,2.298,0.811C32.191,36.439,32.573,35.484,32.934,34.375z">
                                </path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>

        </div>
        <div class="flex max-md:flex-col items-start gap-10">

            <div class="flex-1 max-md:pt-6 self-stretch">
                @yield('content')
            </div>
            <flux:separator class="md:hidden" />

            @hasSection ('sidebar')
            <div class="w-full md:w-[220px]">
                @yield('sidebar')
            </div>
            @endif

        </div>
    </flux:main>
    <div class="[grid-area:footer]">
        <div class="mx-auto max-w-7xl overflow-hidden px-6 py-5 lg:px-8">
            <livewire:subscription>
            <nav class="mt-5 flex flex-wrap justify-center gap-x-12 gap-y-3 text-sm/6" aria-label="Footer">
                <a href="{{ route('page.show', ['slug' => 'gioi-thieu']) }}" class="text-gray-600 dark:text-white">Giới
                    thiệu</a>
                <a href="{{ route('page.show', ['slug' => 'lien-he']) }}" class="text-gray-600 dark:text-white">Liên
                    hệ</a>
                <a href="{{ route('page.show', ['slug' => 'chinh-sach-bao-mat']) }}"
                    class="text-gray-600 dark:text-white">Chính sách bảo mật</a>
                <a href="{{ route('page.show', ['slug' => 'dieu-khoan-su-dung']) }}"
                    class="text-gray-600 dark:text-white">Điều khoản sử dụng</a>

            </nav>
            <div class="mt-3 flex justify-center gap-x-5">
                <a href="#" class="text-gray-600 dark:text-white">
                    <span class="sr-only">Facebook</span>
                    <svg class="size-6 text-sky-900 dark:text-white" fill="currentColor" viewBox="0 0 24 24"
                        aria-hidden="true">
                        <path fill-rule="evenodd"
                            d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                            clip-rule="evenodd" />
                    </svg>
                </a>
                <a href="https://t.me/thongtinvn_group" class="text-gray-600 dark:text-white">
                    <span class="sr-only">Telegram</span>
                    <svg class="size-6 text-sky-500 dark:text-white" fill="currentColor"
                        xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="100" height="100" viewBox="0 0 50 50">
                        <path
                            d="M25,2c12.703,0,23,10.297,23,23S37.703,48,25,48S2,37.703,2,25S12.297,2,25,2z M32.934,34.375	c0.423-1.298,2.405-14.234,2.65-16.783c0.074-0.772-0.17-1.285-0.648-1.514c-0.578-0.278-1.434-0.139-2.427,0.219	c-1.362,0.491-18.774,7.884-19.78,8.312c-0.954,0.405-1.856,0.847-1.856,1.487c0,0.45,0.267,0.703,1.003,0.966	c0.766,0.273,2.695,0.858,3.834,1.172c1.097,0.303,2.346,0.04,3.046-0.395c0.742-0.461,9.305-6.191,9.92-6.693	c0.614-0.502,1.104,0.141,0.602,0.644c-0.502,0.502-6.38,6.207-7.155,6.997c-0.941,0.959-0.273,1.953,0.358,2.351	c0.721,0.454,5.906,3.932,6.687,4.49c0.781,0.558,1.573,0.811,2.298,0.811C32.191,36.439,32.573,35.484,32.934,34.375z">
                        </path>
                    </svg>
                </a>
            </div>
            <p class="mt-3 text-center text-sm/6 text-gray-600 dark:text-white">&copy; 2025 thongtin.vn, Inc. All rights
                reserved.</p>

        </div>

    </div>

    <flux:toast position="bottom right" />
    @fluxScripts
</body>

</html>
