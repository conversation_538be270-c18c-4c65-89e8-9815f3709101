@extends('layout.master')
@section('content')
<div class="grid sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-4 gap-4 items-center mt-5 items-stretch">
    <div class="rounded-xl bg-white dark:bg-white/10 border border-zinc-200 dark:border-white/10 flex flex-col items-center justify-center gap-2 p-5">
        <flux:icon.bolt class="text-amber-500 dark:text-amber-300" />
        <span class="text-center">Cập nhật liên tục</span>
    </div>
    <div class="rounded-xl bg-white dark:bg-white/10 border border-zinc-200 dark:border-white/10 flex flex-col items-center justify-center gap-2 p-5">
        <flux:icon.briefcase />
        <span class="text-center">Hơn 2 triệu doanh nghiệp</span>
    </div>
    <div class="rounded-xl bg-white dark:bg-white/10 border border-zinc-200 dark:border-white/10 flex flex-col items-center justify-center gap-2 p-5">
        <flux:icon.check-badge class="text-emerald-500 dark:text-emerald-300" />
        <span class="text-center">Thông tin chính xác</span>
    </div>
    <div class="rounded-xl bg-white dark:bg-white/10 border border-zinc-200 dark:border-white/10 flex flex-col items-center justify-center gap-2 p-5">
        <flux:icon.map-pin class="text-red-500 dark:text-red-300" />
        <span class="text-center">64 Tỉnh/TP</span>
    </div>
</div>
<div class="mt-5">
    <flux:heading level="1" class="text-xl">Tra cứu mã số thuế doanh nghiệp & cá nhân chính xác, cập nhật mới nhất</flux:heading>
    <flux:separator />
</div>
<livewire:components.tax.tax-businesses lazy>
@endsection
@section('sidebar')
<livewire:components.province>
@endsection