@extends('layout.master')
@section('content')
@push('title')
Tra cứu phạt nguội
@endpush
<livewire:tra-cuu-phat-nguoi>
<div class="mt-5">
    <flux:heading size="xl" level="1">Tra cứu phạt nguội ô tô, xe máy toàn quốc</flux:heading>
    <flux:table class="mt-5">
    <flux:table.columns>
        <flux:table.column>Biển số</flux:table.column>
        <flux:table.column>Vi phạm</flux:table.column>
        <flux:table.column>Trạng thái</flux:table.column>
        <flux:table.column>Thời gian vi phạm</flux:table.column>
    </flux:table.columns>

    <flux:table.rows>
        @if ($violations)
            @foreach ($violations as $violation)
            <flux:table.row>
                <flux:table.cell class="font-medium">
                    <a href="{{ route('detai.phat-nguoi', ['bks' => $violation->vehicle->number]) }}">
                        <div class="flex flex-col border-2 border-black rounded-md w-[140px] items-center {{ str_contains($violation->color, 'vàng') ? 'bg-amber-300': 'bg-white' }}">
                            <span class="font-bold text-xl text-[#000]">{{ formatVehicleNumber($violation->vehicle->number) }}</span>
                        </div>
                    </a>
                </flux:table.cell>
                <flux:table.cell class="text-wrap">{{ $violation->violation }}</flux:table.cell>
                <flux:table.cell>
                    @if ($violation->status == 'Chưa xử phạt')
                    <flux:badge color="red">{{ $violation->status }}</flux:badge>
                    @else
                    <flux:badge color="lime">{{ $violation->status }}</flux:badge>
                    @endif
                </flux:table.cell>
                <flux:table.cell>{{ now()->parse($violation->time)->format("H:i, d-m-Y") }}</flux:table.cell>
            </flux:table.row>
            @endforeach
        @endif
        
    </flux:table.rows>
    </flux:table>
</div>
@endsection


