@extends('layout.master')
@section('content')
<flux:breadcrumbs>
    <flux:breadcrumbs.item href="{{ route('home') }}" icon="home" />
    <flux:breadcrumbs.item href="{{ route('phat-nguoi') }}">Tra cứu phạt nguội</flux:breadcrumbs.item>
    <flux:breadcrumbs.item>{{ formatVehicleNumber($vehicle->number) }}</flux:breadcrumbs.item>
</flux:breadcrumbs>
@push('title')
Phạt nguội biển số xe: {{ formatVehicleNumber($vehicle->number) }}
@endpush
<div class="mt-5">
    <livewire:tra-cuu-phat-nguoi>
</div>
<div class="mt-5">
    <div>
        <flux:heading size="xl" level="1">Phạt nguội biển số xe: {{ formatVehicleNumber($vehicle->number) }} - có {{ count($vehicle->violations) }} phạt nguội.</flux:heading>
        <flux:separator />
        <div class="flex justify-center mt-5 mb-5">
            <div class="flex flex-col border-4 rounded-md w-[150px] items-center text-[#000] {{ str_contains($vehicle->violations->first()->color, 'vàng') ? 'bg-amber-300': 'bg-white' }}">
                <span class="font-bold text-3xl">{{ firstVehicleNumber($vehicle->number) }}</span>
                <span class="font-bold text-3xl">{{ endVehicleNumber($vehicle->number) }}</span>
            </div>
        </div>
                @foreach ($vehicle->violations as $violation)
                <div class="grid sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-2 items-center mt-5 items-stretch">
                    <div class="p-6 bg-white dark:bg-white/10 border border-zinc-200 dark:border-white/10  flex flex-col rounded-t-lg md:rounded-t-lg lg:rounded-none lg:rounded-l-lg
                    watermark-status {{ $violation->status == 'Đã xử phạt'? 'watermark-sanctioned' :''}}">
                        <flux:heading size="lg">Lỗi phạt nguội</flux:heading>
                        <ul>
                            <li class="mt-1"><flux:icon.calendar-days  class="inline-flex size-6"/> <span class="font-medium">Thời gian vi phạm:</span> {{ now()->parse($violation->time)->format('H:i, d-m-Y') }}</li>
                            <li class="mt-1"><flux:icon.map-pin  class="inline-flex size-6"/> <span class="font-medium">Địa điểm vi phạm:</span> {{ $violation->location }}</li>
                            <li class="mt-1"><flux:icon.megaphone  class="inline-flex size-6"/> <span class="font-medium">Hành vi vi phạm:</span> {{ $violation->violation }}</li>
                        </ul>
                    </div>
                    <div class="p-6 bg-white dark:bg-white/10 border border-zinc-200 dark:border-white/10  flex flex-col rounded-b-lg md:rounded-b-lg lg:rounded-none lg:rounded-r-lg">
                        <flux:heading size="lg">Nơi giải quyết</flux:heading>
                        <ul>
                            @foreach ($violation->place_of_solution as $place)
                            <li class="mt-1">{{ $place }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
                @endforeach
    </div>
</div>
@endsection


