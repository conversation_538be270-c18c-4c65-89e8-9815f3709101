@extends('layout.master')
@section('content')
<flux:breadcrumbs>
    <flux:breadcrumbs.item href="{{ route('home') }}" icon="home" />
    <flux:breadcrumbs.item href="{{ route('provinces') }}">Tỉnh/Thành Phố</flux:breadcrumbs.item>
    @if ($province)
    <flux:breadcrumbs.item>{{ $province->full_name }}</flux:breadcrumbs.item>
    @endif
    @if ($district)
    <flux:breadcrumbs.item href="{{ route('search-by-province', ['provinceCode' => \Str::slug($district->province->full_name) .'-'.$district->province->code]) }}">{{ $district->province->full_name }}</flux:breadcrumbs.item>
    <flux:breadcrumbs.item>{{ $district->full_name }}</flux:breadcrumbs.item>
    @endif
    @if ($ward)
    <flux:breadcrumbs.item href="{{ route('search-by-province', ['provinceCode' => \Str::slug($ward->district->province->full_name) .'-'.$ward->district->province->code]) }}">{{ $ward->district->province->full_name }}</flux:breadcrumbs.item>
    <flux:breadcrumbs.item href="{{ route('search-by-province', ['provinceCode' => \Str::slug($ward->district->full_name) .'-'.$ward->district->code]) }}">{{ $ward->district->full_name }}</flux:breadcrumbs.item>
    <flux:breadcrumbs.item>{{ $ward->full_name }}</flux:breadcrumbs.item>
    @endif
</flux:breadcrumbs>
<div class="flex flex-col gap-5 mt-5">
    @if ($tax_businesses)
        @foreach ($tax_businesses as $tax)
        <div>
            <flux:heading><a href="{{ route('detail.business', ['taxSlug' => $tax->tax_id.'-'.\Str::slug($tax->name)]) }}" class="text-blue-600 font-medium">{{ $tax->name }}</a></flux:heading>
            <flux:subheading><div class="flex items-center gap-2"><flux:icon.hashtag class="size-3"/> Mã số thuế: {{ $tax->tax_id }}</div></flux:subheading>
            <flux:subheading><div class="flex items-center gap-2"><flux:icon.user class="size-3"/> Người đại diện: <span class="text-blue-600 font-medium">{{ $tax->representative_name }}</span></div></flux:subheading>
            <flux:subheading><div class="flex items-center gap-2"><flux:icon.map-pin class="size-3"/> {{ $tax->address }}</div></flux:subheading>
            <flux:separator class="mt-5"/>
        </div>
        @endforeach

        <div class="mt-5">
            {{ $tax_businesses->links('vendor/pagination/tailwind') }}
        </div>
    @endif
</div>
@endsection
@section('sidebar')
@if ($province)
    <livewire:components.district :province_code="$province->code">
@endif
@if ($district)
    <livewire:components.ward :district_code="$district->code">
@endif
@if ($ward)
    <livewire:components.ward :district_code="$ward->district->code">
@endif
@endsection