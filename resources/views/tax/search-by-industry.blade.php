@extends('layout.master')
@section('content')
<flux:breadcrumbs>
    <flux:breadcrumbs.item href="{{ route('home') }}" icon="home" />
    <flux:breadcrumbs.item href="{{ route('industries') }}">Ngành nghề</flux:breadcrumbs.item>
    <flux:breadcrumbs.item>{{ $industry->name }}</flux:breadcrumbs.item>
</flux:breadcrumbs>
<flux:heading level="1" size="lg" class="mt-5">Tra cứu mã số thuế doanh nghiệp ngành {{ $industry->name }}</flux:heading>
<div class="flex flex-col gap-5 mt-5">
    @if ($tax_businesses)
        @foreach ($tax_businesses as $tax)
        <div>
            <flux:heading><a href="{{ route('detail.business', ['taxSlug' => $tax->tax_id.'-'.\Str::slug($tax->name)]) }}" class="text-blue-600 font-medium">{{ $tax->name }}</a></flux:heading>
            <flux:subheading><div class="flex items-center gap-2"><flux:icon.hashtag class="size-3"/> Mã số thuế: {{ $tax->tax_id }}</div></flux:subheading>
            <flux:subheading><div class="flex items-center gap-2"><flux:icon.user class="size-3"/> Người đại diện: <span class="text-blue-600 font-medium">{{ $tax->representative_name }}</span></div></flux:subheading>
            <flux:subheading><div class="flex items-center gap-2"><flux:icon.map-pin class="size-3"/> {{ $tax->address }}</div></flux:subheading>
            <flux:separator class="mt-5"/>
        </div>
        @endforeach

        <div class="mt-5">
            {{ $tax_businesses->links('vendor/pagination/tailwind') }}
        </div>
    @endif
</div>
@endsection
@section('sidebar')
<livewire:components.province>
@endsection