@extends('layout.master')
@section('content')
<flux:breadcrumbs>
    <flux:breadcrumbs.item href="{{ route('home') }}" icon="home" />
    <flux:breadcrumbs.item>Mã số thuế doanh nghiệp</flux:breadcrumbs.item>
    <flux:breadcrumbs.item>{{ $business->name }}</flux:breadcrumbs.item>
</flux:breadcrumbs>
@push('title')
{{ $business->tax_id }} - {{ $business->name }}
@endpush
<div class="mt-5">
    <flux:card class="space-y-6">
        <livewire:components.detail-business :business="$business">
    </flux:card>
    @if (isset($industries))
    <flux:card class="space-y-6 mt-5">
        <flux:heading size="xl">Ngành nghề kinh doanh</flux:heading>
        <flux:separator />
        <flux:table>
            <flux:table.rows>
                @foreach ($industries as $industry)

                <flux:table.row>
                    <flux:table.cell class="flex items-center gap-2 font-medium">
                        <a href="{{ route('search-by-industry', ['industryCode' => \Str::slug($industry->name) .'-'.$industry->code]) }}">
                            {{ $industry->code }}
                        </a>
                    </flux:table.cell>
                    <flux:table.cell class="text-wrap">
                        <a href="{{ route('search-by-industry', ['industryCode' => \Str::slug($industry->name) .'-'.$industry->code]) }}">
                            {{ $industry->name }}
                        </a>
                        @if ($industry->primary)
                        <flux:badge color="rose">Ngành nghề chính</flux:badge>
                        @endif
                    </flux:table.cell>
                </flux:table.row>

                @endforeach


            </flux:table.rows>
        </flux:table>
    </flux:card>
    @endif
    <flux:card class="space-y-6 mt-5">
        <livewire:ai-suggestion-domain lazy :business="$business">
    </flux:card>
</div>
@endsection
@section('sidebar')
@if ($business->ward && $business->ward->district)
    <livewire:components.ward :district_code="$business->ward->district->code">
@endif
@if ($business->wardv2)
    <livewire:components.wardv2 :province_code="$business->wardv2->province->province_code" :business="$business">
@endif
@endsection
