<div>
    <flux:modal.trigger name="search-{{$position}}" shortcut="cmd.k">
        <flux:input class="cursor-pointer hover:border-emerald-600 transition:border-color" size="sm" as="button" placeholder="Tra cứu..." icon="magnifying-glass" kbd="⌘K" />
    </flux:modal.trigger>

    <flux:modal name="search-{{$position}}" class="w-full">
        <flux:radio.group wire:model.live="type" label="Tra cứu mã số thuế doanh nghi<PERSON>, c<PERSON> nhân, phạt nguội" variant="cards" class="max-sm:flex-col">
            <flux:radio value="business" label="Doanh nghiệp" class="cursor-pointer"/>
            <flux:radio value="personal" label="Cá nhân" class="cursor-pointer"/>
            <flux:radio value="violation" label="Phạt nguội" class="cursor-pointer"/>
        </flux:radio.group>
        <flux:input icon="magnifying-glass" class="mt-5" placeholder="{{ $placeholder }}" wire:model='searchText'/>
        <flux:error name='searchText'/>
        <flux:button variant="primary" class="mt-5" wire:click='search'>Tra cứu</flux:button>

        @if ($type == 'violation' && $emptyViolation)
        <div class="flex flex-col items-center justify-center gap-2">
            <flux:icon.check-circle class="size-20 text-green-600"/>
            <flux:heading size="xl" class="items-center justify-center">Không tìm thấy phạt nguội biển số {{ formatVehicleNumber($searchText) }}!</flux:heading>
        </div>
        @endif
    </flux:modal>
</div>
