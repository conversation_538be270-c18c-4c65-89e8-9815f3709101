<div class="space-y-6">
    <!-- Header -->
    <div class="text-center">
        <flux:heading size="xl" class="text-gray-900 dark:text-white">
            Đơn <PERSON><PERSON>nh <PERSON> Nam (Dự <PERSON>ế<PERSON>)
        </flux:heading>
        <p class="mt-2 text-gray-600 dark:text-gray-300">
            Danh mục dự kiến ĐVHC theo Công văn số 2896/BNV-CQĐP của Bộ Nội Vụ
        </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="col-span-2">
            <!-- Statistics -->
            <div class="bg-gray-50 dark:bg-zinc-900 rounded-lg border border-gray-200 dark:border-zinc-700 p-6 h-full">
                <flux:heading size="lg" class="text-gray-900 dark:text-white mb-4">
                    Thống kê
                </flux:heading>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div
                        class="text-center p-4 bg-white dark:bg-zinc-800 rounded-lg border border-gray-200 dark:border-zinc-700">
                        <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ count($provinces) }}</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Tỉnh/Thành phố</div>
                    </div>

                    <div
                        class="text-center p-4 bg-white dark:bg-zinc-800 rounded-lg border border-gray-200 dark:border-zinc-700">
                        <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $wardsCount }}</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Phường/Xã</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="col-span-2 flex flex-col gap-5">
            <!-- Form Section -->
            <div class="bg-white dark:bg-zinc-800 rounded-lg border border-gray-200 dark:border-zinc-700 p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Province Selection -->
                    <div>
                        <flux:field>
                            <flux:label>Tỉnh/Thành phố</flux:label>
                            <flux:select wire:model.live="selectedProvince" placeholder="Chọn tỉnh/thành phố..."
                                variant="listbox" searchable>
                                <flux:select.option value="">-- Chọn tỉnh/thành phố --</flux:select.option>
                                @foreach($provinces as $province)
                                <flux:select.option value="{{ $province->province_code }}">{{ $province->name }}
                                </flux:select.option>
                                @endforeach
                            </flux:select>
                        </flux:field>
                    </div>

                    <!-- Ward Selection -->
                    <div>
                        <flux:field>
                            <flux:label>Phường/Xã</flux:label>
                            <flux:select wire:model.live="selectedWard" placeholder="Chọn phường/xã..."
                                :disabled="empty($selectedProvince)" variant="listbox" searchable>
                                <flux:select.option value="">-- Chọn phường/xã --</flux:select.option>
                                @foreach($wards as $ward)
                                <flux:select.option value="{{ $ward->ward_code }}">{{ $ward->name }}
                                </flux:select.option>
                                @endforeach
                            </flux:select>
                            @if(!$selectedProvince)
                            <flux:description>Vui lòng chọn tỉnh/thành phố trước</flux:description>
                            @endif
                        </flux:field>
                    </div>
                </div>

                <!-- Reset Button -->
                <div class="mt-6 flex justify-center">
                    <flux:button wire:click="resetForm" variant="outline" icon="arrow-path">
                        Đặt lại
                    </flux:button>
                </div>
            </div>

            <!-- Information Display -->
            @if($selectedProvinceInfo || $selectedWardInfo)
            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 p-6">
                <flux:heading size="lg" class="text-blue-900 dark:text-blue-100 mb-4">
                    Thông tin đã chọn
                </flux:heading>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Province Info -->
                    @if($selectedProvinceInfo)
                    <div class="space-y-3">
                        <flux:heading class="text-blue-800 dark:text-blue-200">
                            Tỉnh/Thành phố
                        </flux:heading>
                        <div
                            class="bg-white dark:bg-zinc-800 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700 dark:text-gray-300">Tên:</span>
                                    <span class="text-gray-900 dark:text-white">{{ $selectedProvinceInfo->name }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700 dark:text-gray-300">Mã:</span>
                                    <span class="text-gray-900 dark:text-white font-mono">{{
                                        $selectedProvinceInfo->province_code }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700 dark:text-gray-300">Số Phường/Xã:</span>
                                    <span class="text-gray-900 dark:text-white font-mono">{{ $selectedProvinceInfo->wards()->count() }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Ward Info -->
                    @if($selectedWardInfo)
                    <div class="space-y-3">
                        <flux:heading class="text-blue-800 dark:text-blue-200">
                            Phường/Xã
                        </flux:heading>
                        <div
                            class="bg-white dark:bg-zinc-800 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700 dark:text-gray-300">Tên:</span>
                                    <span class="text-gray-900 dark:text-white">{{ $selectedWardInfo->name }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-700 dark:text-gray-300">Mã:</span>
                                    <span class="text-gray-900 dark:text-white font-mono">{{
                                        $selectedWardInfo->ward_code }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>
        <div class="col-span-2">
            <div class="bg-white dark:bg-zinc-800 rounded-lg border border-gray-200 dark:border-zinc-700 p-6">
                <flux:heading size="lg" class="text-gray-900 dark:text-white mb-4">
                    Chuyển đổi đơn vị hành chính cũ sang đơn vị hành chính mới
                </flux:heading>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Province Selection -->
                    <div>
                        <flux:field>
                            <flux:label>Tỉnh/Thành phố</flux:label>
                            <flux:select wire:model.live="selectedOldProvince" placeholder="Chọn tỉnh/thành phố..."
                                variant="listbox" searchable>
                                <flux:select.option value="">-- Chọn tỉnh/thành phố --</flux:select.option>
                                @foreach($old_provinces as $old_province)
                                <flux:select.option value="{{ $old_province->code }}">{{ $old_province->full_name }}
                                </flux:select.option>
                                @endforeach
                            </flux:select>
                        </flux:field>
                    </div>
                    <div>
                        <flux:field>
                            <flux:label>Quận/Huyện</flux:label>
                            <flux:select wire:model.live="selectedOldDistrict" placeholder="Chọn quận/huyện..."
                                :disabled="empty($selectedOldProvince)" variant="listbox" searchable>
                                <flux:select.option value="">-- Chọn quận/huyện --</flux:select.option>
                                @foreach($old_districts as $old_district)
                                <flux:select.option value="{{ $old_district->code }}">{{ $old_district->full_name }}
                                </flux:select.option>
                                @endforeach
                            </flux:select>
                            @if(!$selectedOldProvince)
                            <flux:description>Vui lòng chọn tỉnh/thành phố trước</flux:description>
                            @endif
                        </flux:field>
                    </div>
                    <div>
                        <flux:field>
                            <flux:label>Phường/Xã</flux:label>
                            <flux:select wire:model.live="selectedOldWard" placeholder="Chọn phường/xã..."
                                :disabled="empty($selectedOldDistrict)" variant="listbox" searchable>
                                <flux:select.option value="">-- Chọn phường/xã --</flux:select.option>
                                @foreach($old_wards as $old_ward)
                                <flux:select.option value="{{ $old_ward->code }}">{{ $old_ward->full_name }}
                                </flux:select.option>
                                @endforeach
                            </flux:select>
                            @if(!$selectedOldDistrict)
                            <flux:description>Vui lòng chọn quận/huyện trước</flux:description>
                            @endif
                        </flux:field>
                    </div>
                </div>
                <div class="mt-6 flex justify-center">
                    <flux:button wire:click="convert" variant="primary" icon="arrow-path">
                        Chuyển đổi
                    </flux:button>
                </div>
                <div>
                    @if($newAddress && count($newAddress))
                    <div class="bg-white dark:bg-zinc-800 rounded-lg p-4 border border-blue-200 dark:border-blue-700 mt-6">
                        <flux:heading class="text-blue-800!" size="lg">
                            Thông tin cũ
                        </flux:heading>
                        {{ $newAddress[0]->ward_name }}, {{ $newAddress[0]->district_name }}, {{ $newAddress[0]->province_name }}
                    </div>
                    <div class="mt-5">
                        <flux:heading class="text-green-800!" size="lg">
                            Thông tin mới (có {{ count($newAddress) }} gợi ý)
                        </flux:heading>
                    </div>
                    @foreach ($newAddress as $item)
                    <div class="space-y-3 mt-5">
                        <div class="bg-white dark:bg-zinc-800 rounded-lg p-4 border border-green-700 dark:border-green-700">
                            <div class="space-y-2">
                                <div class="flex gap-1">
                                    <span class="font-bold text-gray-700 dark:text-gray-300">Tỉnh/Thành phố:</span>
                                    <span class="text-gray-900 dark:text-white">{{ $item->new_province }} (Mã: {{ $item->new_province_code }})</span>
                                </div>
                                <div class="flex gap-1">
                                    <span class="font-bold text-gray-700 dark:text-gray-300">Phường/Xã:</span>
                                    <span class="text-gray-900 dark:text-white">{{ $item->new_ward }} (Mã: {{ $item->new_ward_code }})</span>
                                </div>
                                {{-- <div class="flex gap-1">
                                    <span class="font-bold text-gray-700 dark:text-gray-300">Trạng thái:</span>
                                    <span class="text-gray-900 dark:text-white">{{ $item->not_sure ? 'Không chắc chắn' : 'Chính xác' }}</span>
                                </div> --}}
                                <div class="flex gap-1">
                                    <span class="text-gray-900 dark:text-white"><span class="font-bold text-gray-700 dark:text-gray-300">Nội dung gộp:</span> {{ $item->new_detail }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                    @endif
                </div>

                
            </div>
        </div>
    </div>
</div>