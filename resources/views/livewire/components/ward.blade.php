<div>
    <flux:heading><a href="{{ route('search-by-province', ['provinceCode' => \Str::slug($district->province->full_name) .'-'.$district->province->code]) }}">{{ $district->province->full_name }} </a> - {{ $district->full_name }}</flux:heading>
    <flux:separator class="mb-5"/>
    <flux:navlist>
        @if ($wards)
            @foreach ($wards as $item)
            <flux:navlist.item icon="chevron-double-right" href="{{ route('search-by-province', ['provinceCode' => \Str::slug($item->full_name).'-'.$item->code]) }}">{{ $item->full_name }}</flux:navlist.item>
            @endforeach
        @endif
    </flux:navlist>
</div>
