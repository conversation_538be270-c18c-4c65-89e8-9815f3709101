<div>
    <flux:heading size="lg" level="1" class="text-blue-600 dark:text-white font-medium">{{ $business->tax_id }} - {{ $business->name }}</flux:heading>
    <flux:separator />
    <flux:table>
        <flux:table.rows>
            <flux:table.row>
                <flux:table.cell class="flex items-center gap-2 font-medium"><flux:icon.hashtag class="size-5"/> Mã số thuế</flux:table.cell>
                <flux:table.cell>{{ $business->tax_id ?? '' }}</flux:table.cell>
            </flux:table.row>
            <flux:table.row>
                <flux:table.cell class="flex items-center gap-2"><flux:icon.map-pin class="size-5"/> Đ<PERSON><PERSON> chỉ</flux:table.cell>
                <flux:table.cell class="text-wrap">{{ $business->address ?? '' }}</flux:table.cell>
            </flux:table.row>
            <flux:table.row>
                <flux:table.cell class="flex items-center gap-2"><flux:icon.user class="size-5"/> Ngư<PERSON>i đại diện</flux:table.cell>
                <flux:table.cell>
                    <span class="font-medium">{{ $business->representative_name ?? '' }}</span>
                    @if ($business->id_number != '' && count($business->relatedTaxes) > 1)
                    <div class="mt-2">
                        Ngoài ra <span class="font-medium">{{ $business->representative_name ?? '' }}</span> còn đại diện các doanh nghiệp: 
                        <ul class="list-disc">
                            @foreach ($business->relatedTaxes as $itemRelated)
                            @if ($itemRelated->tax_id != $business->tax_id)
                            <li class="ml-5 text-wrap"><a href="{{ route('detail.business', ['taxSlug' => $itemRelated->tax_id.'-'.\Str::slug($itemRelated->name)]) }}">{{ $itemRelated->name }}</a></li>
                                
                            @endif
                            @endforeach
                        </ul>
                    </div>
                    @endif
                    
                </flux:table.cell>
            </flux:table.row>
            @if ($business->director_name)
            <flux:table.row>
                <flux:table.cell class="flex items-center gap-2"><flux:icon.user class="size-5"/> Giám đốc</flux:table.cell>
                <flux:table.cell class="text-wrap"><span class="font-medium">{{ $business->director_name ?? '' }}</span></flux:table.cell>
            </flux:table.row>
            @endif
            @if ($business->chief_accountant)
            <flux:table.row>
                <flux:table.cell class="flex items-center gap-2"><flux:icon.user class="size-5"/> Kế toán trưởng</flux:table.cell>
                <flux:table.cell class="text-wrap"><span class="font-medium">{{ $business->chief_accountant ?? '' }}</span></flux:table.cell>
            </flux:table.row>
            @endif
            <flux:table.row>
                <flux:table.cell class="flex items-center gap-2"><flux:icon.phone class="size-5"/> Số điện thoại</flux:table.cell>
                <flux:table.cell>
                    {{-- {{ $business->phone ? str_replace("/", "", $business->phone) : '' }} --}}
                    Bị ẩn theo yêu cầu của người dùng.
                </flux:table.cell>
            </flux:table.row>
            <flux:table.row>
                <flux:table.cell class="flex items-center gap-2"><flux:icon.calendar-days class="size-5"/> Ngày hoạt động</flux:table.cell>
                <flux:table.cell>{{ $business->establish_date ?? '' }}</flux:table.cell>
            </flux:table.row>
            <flux:table.row>
                <flux:table.cell class="flex items-center gap-2"><flux:icon.user-group class="size-5"/> Quản lý bởi</flux:table.cell>
                <flux:table.cell>{{ $business->management_agency ?? '' }}</flux:table.cell>
            </flux:table.row>
            <flux:table.row>
                <flux:table.cell class="flex items-center gap-2"><flux:icon.information-circle class="size-5"/>  Tình trạng</flux:table.cell>
                <flux:table.cell>
                    @if ($business->status)
                        @if (str_contains($business->status,"Đang hoạt động") || str_contains($business->status,"đang hoạt động"))
                        <flux:badge color="lime">{{ $business->status }}</flux:badge>
                        @else 
                        <flux:badge color="red">{{ $business->status }}</flux:badge>
                        @endif
                    @endif
                </flux:table.cell>
            </flux:table.row>
            {{-- <flux:table.row>
                <flux:table.cell class="flex items-center gap-2"><flux:icon.user-group class="size-5"/> Ngành nghề chính</flux:table.cell>
                <flux:table.cell>{{ $business?->primaryIndustry?->first()?->name }}</flux:table.cell>
            </flux:table.row> --}}
        </flux:table.rows>
    </flux:table>
    @if ($business->updated_at)
    <div class="text-sm text-gray-500 dark:text-white italic">
        Cập nhật mã số thuế <span class="font-medium">{{ $business->tax_id ?? '' }} </span> lần cuối vào {{ $business->updated_at }}. 
        <flux:button size='sm' wire:click="reUpdate('{{$business->tax_id}}')" wire:target="reUpdate('{{$business->tax_id}}')">Cập nhật</flux:button>
    </div>
    @endif
</div>
