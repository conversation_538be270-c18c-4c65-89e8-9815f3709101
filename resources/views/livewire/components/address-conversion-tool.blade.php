<div class="space-y-6">
    <!-- Header -->
    <div class="text-center">
        <flux:heading size="xl" class="text-gray-900 dark:text-white">
            Công cụ chuyển đổi địa chỉ hành chính
        </flux:heading>
        <p class="mt-2 text-gray-600 dark:text-gray-300">
            Tì<PERSON> kiếm tên cũ của đơn vị hành chính trong dữ liệu DVHC và tạo bản ghi chuyển đổi
        </p>
    </div>


    <!-- Statistics -->
    @if($statistics)
    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 p-6">
        <flux:heading size="lg" class="text-blue-900 dark:text-blue-100 mb-4">
            Thống kê dữ liệu DVHC
        </flux:heading>
        
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center p-3 bg-white dark:bg-zinc-800 rounded-lg border border-blue-200 dark:border-blue-700">
                <div class="text-xl font-bold text-blue-600 dark:text-blue-400">{{ number_format($statistics['total_rows']) }}</div>
                <div class="text-xs text-gray-600 dark:text-gray-400">Tổng dòng</div>
            </div>
            
            <div class="text-center p-3 bg-white dark:bg-zinc-800 rounded-lg border border-blue-200 dark:border-blue-700">
                <div class="text-xl font-bold text-green-600 dark:text-green-400">{{ number_format($statistics['unique_provinces']) }}</div>
                <div class="text-xs text-gray-600 dark:text-gray-400">Tỉnh/TP</div>
            </div>
            
            <div class="text-center p-3 bg-white dark:bg-zinc-800 rounded-lg border border-blue-200 dark:border-blue-700">
                <div class="text-xl font-bold text-purple-600 dark:text-purple-400">{{ number_format($statistics['unique_districts']) }}</div>
                <div class="text-xs text-gray-600 dark:text-gray-400">Quận/Huyện</div>
            </div>
            
            <div class="text-center p-3 bg-white dark:bg-zinc-800 rounded-lg border border-blue-200 dark:border-blue-700">
                <div class="text-xl font-bold text-orange-600 dark:text-orange-400">{{ number_format($statistics['estimated_total_wards']) }}</div>
                <div class="text-xs text-gray-600 dark:text-gray-400">Phường/Xã</div>
            </div>
        </div>
    </div>
    @endif

    <!-- Search Form -->
    <div class="bg-white dark:bg-zinc-800 rounded-lg border border-gray-200 dark:border-zinc-700 p-6">
        <flux:heading size="lg" class="text-gray-900 dark:text-white mb-4">
            Tìm kiếm trong dữ liệu DVHC
        </flux:heading>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="md:col-span-2">
                <flux:field>
                    <flux:label>Từ khóa tìm kiếm</flux:label>
                    <flux:input wire:model="searchTerm" placeholder="Nhập tên tỉnh, huyện, xã cần tìm..." />
                    @error('searchTerm')
                        <flux:error>{{ $message }}</flux:error>
                    @enderror
                </flux:field>
            </div>
            
            <div>
                <flux:field>
                    <flux:label>Loại tìm kiếm</flux:label>
                    <flux:select wire:model="searchType">
                        @foreach($searchTypes as $value => $label)
                            <flux:select.option value="{{ $value }}">{{ $label }}</flux:select.option>
                        @endforeach
                    </flux:select>
                </flux:field>
            </div>
        </div>

        <div class="mt-6 flex gap-3">
            <flux:button wire:click="search" variant="primary" icon="magnifying-glass" :loading="$isSearching">
                Tìm kiếm
            </flux:button>
            
            @if($showResults && !empty($searchResults))
                <flux:button wire:click="createAddressConversions" variant="outline" icon="plus">
                    Tạo bản ghi chuyển đổi
                </flux:button>
            @endif
            
            @if($showResults)
                <flux:button wire:click="clearResults" variant="ghost" icon="x-mark">
                    Xóa kết quả
                </flux:button>
            @endif
        </div>
    </div>

    <!-- Search Results -->
    @if($showResults && !empty($searchResults))
    <div class="bg-white dark:bg-zinc-800 rounded-lg border border-gray-200 dark:border-zinc-700 p-6">
        <flux:heading size="lg" class="text-gray-900 dark:text-white mb-4">
            Kết quả tìm kiếm ({{ count($searchResults) }} kết quả)
        </flux:heading>
        
        <div class="space-y-4 max-h-96 overflow-y-auto">
            @foreach($searchResults as $index => $result)
                <div class="border border-gray-200 dark:border-zinc-600 rounded-lg p-4">
                    <div class="flex justify-between items-start mb-2">
                        <flux:heading class="text-gray-800 dark:text-gray-200">
                            Kết quả {{ $index + 1 }}
                        </flux:heading>
                        
                        @if(isset($result['types']))
                            <flux:badge variant="outline">{{ implode(', ', $result['types']) }}</flux:badge>
                        @else
                            <flux:badge variant="outline">{{ $result['type'] }}</flux:badge>
                        @endif
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                        <div>
                            <span class="font-medium text-gray-700 dark:text-gray-300">Tỉnh/TP (hiện tại):</span>
                            <span class="text-gray-900 dark:text-white">{{ $result['current_province'] ?? 'N/A' }}</span>
                        </div>

                        <div>
                            <span class="font-medium text-gray-700 dark:text-gray-300">Quận/Huyện (hiện tại):</span>
                            <span class="text-gray-900 dark:text-white">{{ $result['current_district'] ?? 'N/A' }}</span>
                        </div>

                        <div>
                            <span class="font-medium text-gray-700 dark:text-gray-300">Phường/Xã (cũ):</span>
                            <span class="text-gray-900 dark:text-white">{{ Str::limit($result['old_wards'] ?? $result['all_old_wards'] ?? 'N/A', 50) }}</span>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
    @endif

    <!-- Creation Results -->
    @if($creationResults)
    <div class="bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800 p-6">
        <flux:heading size="lg" class="text-green-900 dark:text-green-100 mb-4">
            Kết quả tạo bản ghi
        </flux:heading>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div class="text-center p-3 bg-white dark:bg-zinc-800 rounded-lg border border-green-200 dark:border-green-700">
                <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $creationResults['total_created'] }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Bản ghi đã tạo</div>
            </div>

            <div class="text-center p-3 bg-white dark:bg-zinc-800 rounded-lg border border-green-200 dark:border-green-700">
                <div class="text-2xl font-bold text-red-600 dark:text-red-400">{{ $creationResults['total_errors'] }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Lỗi</div>
            </div>
        </div>

        @if(!empty($creationResults['created']))
            <flux:heading class="text-green-800 dark:text-green-200 mb-3">
                Mẫu bản ghi đã tạo:
            </flux:heading>

            <div class="space-y-2">
                @foreach(array_slice($creationResults['created'], 0, 3) as $record)
                    <div class="bg-white dark:bg-zinc-800 rounded p-3 border border-green-200 dark:border-green-700">
                        <div class="text-sm">
                            <strong>Cũ:</strong> {{ $record->ward_name }}, {{ $record->district_name }}, {{ $record->province_name }}
                        </div>
                        <div class="text-sm mt-1">
                            <strong>Mới:</strong> {{ $record->new_ward }} ({{ $record->new_ward_code }}), {{ $record->new_province }} ({{ $record->new_province_code }})
                        </div>
                        <div class="text-xs mt-1">
                            <flux:badge variant="{{ $record->success ? 'success' : 'warning' }}">
                                {{ $record->success ? 'Thành công' : 'Không chắc chắn' }}
                            </flux:badge>
                        </div>
                    </div>
                @endforeach
            </div>
        @endif
    </div>
    @endif

    <!-- Recent Conversions -->
    @if($recentConversions->count() > 0)
    <div class="bg-gray-50 dark:bg-zinc-900 rounded-lg border border-gray-200 dark:border-zinc-700 p-6">
        <flux:heading size="lg" class="text-gray-900 dark:text-white mb-4">
            Bản ghi chuyển đổi gần đây
        </flux:heading>

        <div class="space-y-3">
            @foreach($recentConversions as $conversion)
                <div class="bg-white dark:bg-zinc-800 rounded p-3 border border-gray-200 dark:border-zinc-600">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="text-sm">
                                <strong>Cũ:</strong> {{ $conversion->ward_name }}, {{ $conversion->district_name }}, {{ $conversion->province_name }}
                            </div>
                            <div class="text-sm mt-1">
                                <strong>Mới:</strong> {{ $conversion->new_ward }} ({{ $conversion->new_ward_code }}), {{ $conversion->new_province }} ({{ $conversion->new_province_code }})
                            </div>
                            <div class="text-xs text-gray-500 mt-1">
                                {{ $conversion->created_at->diffForHumans() }}
                            </div>
                        </div>

                        <div class="ml-3">
                            <flux:badge variant="{{ $conversion->success ? 'success' : 'warning' }}">
                                {{ $conversion->success ? 'Thành công' : 'Không chắc chắn' }}
                            </flux:badge>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
    @endif

    <!-- Instructions -->
    <div class="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800 p-6">
        <flux:heading class="text-yellow-800 dark:text-yellow-200 mb-3">
            Hướng dẫn sử dụng
        </flux:heading>
        <ul class="space-y-2 text-yellow-700 dark:text-yellow-300">
            <li class="flex items-start">
                <span class="mr-2">1.</span>
                <span>Nhập từ khóa tìm kiếm (tên tỉnh, huyện, xã cũ)</span>
            </li>
            <li class="flex items-start">
                <span class="mr-2">2.</span>
                <span>Chọn loại tìm kiếm để thu hẹp kết quả</span>
            </li>
            <li class="flex items-start">
                <span class="mr-2">3.</span>
                <span>Nhấn "Tìm kiếm" để tìm trong dữ liệu DVHC</span>
            </li>
            <li class="flex items-start">
                <span class="mr-2">4.</span>
                <span>Nhấn "Tạo bản ghi chuyển đổi" để tạo các bản ghi AddressConversion</span>
            </li>
            <li class="flex items-start">
                <span class="mr-2">5.</span>
                <span>Hệ thống sẽ tự động tìm kiếm đơn vị hành chính mới tương ứng</span>
            </li>
        </ul>
    </div>
</div>
