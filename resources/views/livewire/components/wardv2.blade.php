<div>
    <flux:heading><a href="{{ route('search-by-province', ['provinceCode' => \Str::slug($business->wardv2->province->name) .'-'.$business->wardv2->province->province_code]) }}">{{ $business->wardv2->province->name}}</flux:heading>
    <flux:separator class="mb-5"/>
    <flux:navlist>
        @if ($wards)
            @foreach ($wards as $item)
            <flux:navlist.item icon="chevron-double-right" href="{{ route('search-by-province', ['provinceCode' => \Str::slug($item->name).'-'.$item->ward_code]) }}">{{ $item->name }}</flux:navlist.item>
            @endforeach
        @endif
    </flux:navlist>
</div>
