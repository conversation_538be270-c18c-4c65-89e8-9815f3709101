<div class="mt-5">

    <flux:heading size="xl" level="1"><PERSON>ra c<PERSON>u mã số thuế theo ngành nghề</flux:heading>
    <flux:separator />
    <flux:table>
        <flux:table.columns>
            <flux:table.column>Mã</flux:table.column>
            <flux:table.column>Ngành</flux:table.column>
        </flux:table.columns>
        <flux:table.rows>
            @if ($industries)
            @foreach ($industries as $industry)
            <flux:table.row>
                <flux:table.cell class="flex items-center gap-2 font-medium"><a href="{{ route('search-by-industry', ['industryCode' => \Str::slug($industry->name) .'-'.$industry->code]) }}">{{ $industry->code }}</a></flux:table.cell>
                <flux:table.cell class="text-wrap"><a href="{{ route('search-by-industry', ['industryCode' => \Str::slug($industry->name) .'-'.$industry->code]) }}">{{ $industry->name }}</a></flux:table.cell>
            </flux:table.row>
            @endforeach
            @endif

        </flux:table.rows>
    </flux:table>
    <div class="mt-5">
        {{ $industries->links('vendor/pagination/tailwind') }}
    </div>
</div>