<div>
    <flux:heading size="lg" level="1">{{ $personal->tax_id }} - {{ $personal->name }}</flux:heading>
    <flux:separator />
    <flux:table>
        <flux:table.rows>
            <flux:table.row>
                <flux:table.cell class="flex items-center gap-2 font-medium"><flux:icon.hashtag class="size-5"/> Mã số thuế</flux:table.cell>
                <flux:table.cell>{{ $personal->tax_id ?? '' }}</flux:table.cell>
            </flux:table.row>
            <flux:table.row>
                <flux:table.cell class="flex items-center gap-2"><flux:icon.user class="size-5"/> Tên ng<PERSON>ời nộp thuế</flux:table.cell>
                <flux:table.cell>
                    <span class="font-medium">{{ $personal->name ?? '' }}</span>
                    @if (count($personal->relatedTaxes))
                    <div class="mt-2">
                        Là đại diện c<PERSON>c do<PERSON>h nghiệp: 
                        <ul class="list-disc">
                            @foreach ($personal->relatedTaxes as $itemRelated)
                            @if ($itemRelated->tax_id != $personal->tax_id)
                            <li class="ml-5 text-wrap"><a href="{{ route('detail.business', ['taxSlug' => $itemRelated->tax_id.'-'.\Str::slug($itemRelated->name)]) }}">{{ $itemRelated->name }}</a></li>
                                
                            @endif
                            @endforeach
                        </ul>
                    </div>
                    @endif
                </flux:table.cell>
            </flux:table.row>
            <flux:table.row>
                <flux:table.cell class="flex items-center gap-2"><flux:icon.identification class="size-5"/>Số CMT/Thẻ căn cước</flux:table.cell>
                <flux:table.cell>
                    <span>{{ $personal->id_number ?? '' }}</span>
                </flux:table.cell>
            </flux:table.row>
            <flux:table.row>
                <flux:table.cell class="flex items-center gap-2"><flux:icon.map-pin class="size-5"/> Địa chỉ</flux:table.cell>
                <flux:table.cell class="text-wrap">{{ $personal->address ?? '' }}</flux:table.cell>
            </flux:table.row>
            
            <flux:table.row>
                <flux:table.cell class="flex items-center gap-2"><flux:icon.phone class="size-5"/> Số điện thoại</flux:table.cell>
                <flux:table.cell>{{ $personal->phone ? str_replace("/", "", $personal->phone) : '' }}</flux:table.cell>
            </flux:table.row>
            
            <flux:table.row>
                <flux:table.cell class="flex items-center gap-2"><flux:icon.user-group class="size-5"/> Nơi đăng ký quản lý</flux:table.cell>
                <flux:table.cell>{{ $personal->tax_management_office ?? '' }}</flux:table.cell>
            </flux:table.row>
            <flux:table.row>
                <flux:table.cell class="flex items-center gap-2"><flux:icon.information-circle class="size-5"/>  Tình trạng</flux:table.cell>
                <flux:table.cell>
                    @if ($personal->status)
                        @if (str_contains($personal->status,"Đang hoạt động") || str_contains($personal->status,"đang hoạt động"))
                        <flux:badge color="lime">{{ $personal->status }}</flux:badge>
                        @else 
                        <flux:badge color="red">{{ $personal->status }}</flux:badge>
                        @endif
                    @endif
                </flux:table.cell>
            </flux:table.row>
            <flux:table.row>
                <flux:table.cell class="flex items-center gap-2"><flux:icon.calendar-days class="size-5"/> Ngày cấp MST</flux:table.cell>
                <flux:table.cell>{{ $personal->issue_date ?? '' }}</flux:table.cell>
            </flux:table.row>
            <flux:table.row>
                <flux:table.cell class="flex items-center gap-2"><flux:icon.calendar-days class="size-5"/> Ngày đóng MST</flux:table.cell>
                <flux:table.cell>{{ $personal->close_date ?? '' }}</flux:table.cell>
            </flux:table.row>
        </flux:table.rows>
    </flux:table>
    @if ($personal->updated_at)
    <div class="text-sm text-gray-500 dark:text-white italic">
        Cập nhật mã số thuế <span class="font-medium">{{ $personal->tax_id ?? '' }} </span> lần cuối vào {{ $personal->updated_at }}. 
        {{-- <flux:button size='sm' wire:click="reUpdate('{{$personal->tax_id}}')" wire:target="reUpdate('{{$personal->tax_id}}')">Cập nhật</flux:button> --}}
    </div>
    @endif
</div>
