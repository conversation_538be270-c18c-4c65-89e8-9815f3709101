<flux:card class="space-y-6">
    <flux:field>
        <flux:label>Tra cứu phạt nguội</flux:label>
        <flux:input wire:model="bks" placeholder="Nhập biển số xe để tra cứu phạt nguội" />
        <flux:subheading class="mt-1">VD: 30A12345, 30LD12345</flux:subheading>
        <flux:heading level="2">Hướng dẫn cách tra cứu phạt nguội xe ô tô, xe máy trên <PERSON>.vn</flux:heading>
        <flux:error name="bks" />
    </flux:field>
    <flux:button icon="magnifying-glass" variant='primary' wire:click='search' wire:target='search'>Tra cứu
    </flux:button>
    @if ($empty)
    <div class="flex flex-col items-center justify-center gap-2">
        <flux:icon.check-circle class="size-20 text-green-600"/>
        <flux:heading size="xl" class="items-center justify-center"><PERSON><PERSON><PERSON><PERSON> tìm thấy phạt nguội biển số {{ formatVehicleNumber($bks) }}!</flux:heading>
    </div>
    @endif
</flux:card>