@import "tailwindcss";
@source "../views";
@import '../../vendor/livewire/flux/dist/flux.css';

/* Required for dark mode in Flux... */
@custom-variant dark (&:where(.dark, .dark *));

@theme {
    --color-accent: var(--color-emerald-600);
    --color-accent-content: var(--color-emerald-600);
    --color-accent-foreground: var(--color-white);
}

@layer theme {
    .dark {
        --color-accent: var(--color-emerald-600);
        --color-accent-content: var(--color-emerald-400);
        --color-accent-foreground: var(--color-white);
    }
}

@layer base {
    button{
        @apply cursor-pointer
    }
}

div.watermark-status {
    position: relative
}

div.watermark-status:after {
    content: "Chưa xử phạt";
    color: red;
    font-size: 20px;
    font-weight: 700;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%) rotate(-30deg);
    opacity: .4;
    pointer-events: none;
    border: 5px double red;
    padding: 10px;
    border-radius: 4px;
    width: 170px;
    text-align: center
}

div.watermark-sanctioned:after {
    content: "Đã xử phạt";
    color: #009866;
    border-color: #009866;
}

@font-face {
    font-family: 'FontXe';
    src: url('/resources/fonts/Soxe2banh.ttf');
}

.bks {
    font-family: FontXe;
}

.page-content ul {
    list-style: disc;
}
.page-content ul li {
    margin-left: 50px;
}