<?php

function formatVehicleNumber(string $string): string
{
    // return preg_replace('/^(\d{2}[a-zA-Z]{1,})(\d{3})(\d{2})$/', '$1-$2.$3', strtoupper($string));
    return preg_replace_callback(
        '/^(\d{2}[a-zA-Z]{1,})(\d{3})(\d{1,2})$/',
        function ($matches) {
            if (strlen($matches[3]) == 2) {
                return "{$matches[1]}-{$matches[2]}.{$matches[3]}";
            }
            return "{$matches[1]}-{$matches[2]}{$matches[3]}";
        },
        $string
    );
}

function firstVehicleNumber(string $string): string
{
    return preg_replace('/^(\d{2}[a-zA-Z]{1,})(\d{3})(\d{1,2})$/', '$1', strtoupper($string));
}

function endVehicleNumber(string $string): string
{
    return preg_replace_callback(
        '/^(\d{2}[a-zA-Z]{1,})(\d{3})(\d{1,2})$/',
        function ($matches) {
            if (strlen($matches[3]) == 2) {
                return "{$matches[2]}.{$matches[3]}";
            }
            return "{$matches[2]}{$matches[3]}";
        },
        $string
    );
}
