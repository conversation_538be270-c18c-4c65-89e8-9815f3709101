<?php

namespace App\Http\Controllers;

use App\Models\Page;
use Artesaos\SEOTools\Facades\JsonLd;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Http\Request;

class PageController extends Controller
{
    public function show($slug)
    {
        $page = Page::where('slug', $slug)->where('visible', true)->first();
        abort_if(!$page, 404);

        SEOMeta::setTitle($page->name);
        SEOMeta::setDescription($page->name);
        JsonLd::setTitle($page->name);
        JsonLd::setDescription($page->name);
        JsonLd::addValues([
            'alternateName' => $page->name,
            'headline' => $page->name,
            "breadcrumb" => [
                "@type" => "BreadcrumbList",
                "itemListElement" => [
                    [
                        "@type" => "ListItem",
                        "position" => 1,
                        "name" => "Trang chủ",
                        "item" => route('home')
                    ],
                    [
                        "@type" => "ListItem",
                        "position" => 2,
                        "name" => $page->name,
                        "item" => route('page.show', ['slug' => $page->slug])
                    ],
                ]
            ],
            "publisher" => [
                "@type" => "Organization",
                "name" => "Thongtin.vn",
                "url" => route('home'),
                "logo" => asset("images/logo/thongtin_light.png"),
                "contactPoint" => [
                    "@type" => "ContactPoint",
                    // "telephone" => "+84-123-456-789",
                    "contactType" => "customer service"
                ]
            ]
        ]);

        return view('page.show', compact('page'));
    }
}
