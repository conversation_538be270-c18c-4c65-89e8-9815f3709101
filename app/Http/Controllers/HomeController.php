<?php

namespace App\Http\Controllers;

use App\Models\District;
use App\Models\Industry;
use App\Models\Province;
use App\Models\TaxBusiness;
use App\Models\TaxIndustry;
use App\Models\TaxPersonal;
use App\Models\VehicleNumber;
use App\Models\Violation;
use App\Models\Ward;
use Artesaos\SEOTools\Facades\JsonLd;
use Artesaos\SEOTools\Facades\SEOMeta;
use Elastic\Elasticsearch\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MatchPhraseQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MatchQuery;

class HomeController extends Controller
{
    public function index()
    {
        SEOMeta::setTitle('Tra cứu mã số thuế doanh nghiệp, cá nhân');
        SEOMeta::setDescription('Tra cứu mã số thuế doanh nghiệp & c<PERSON> nhân mới nhất. Cập nhật dữ liệu liên tục, hỗ trợ tìm kiếm trên Facebook & Zalo. Kiểm tra ngay!');
        JsonLd::setTitle('Tra cứu mã số thuế doanh nghiệp, cá nhân');
        JsonLd::setDescription('Tra cứu mã số thuế doanh nghiệp & cá nhân mới nhất. Cập nhật dữ liệu liên tục, hỗ trợ tìm kiếm trên Facebook & Zalo. Kiểm tra ngay!');
        JsonLd::addValues([
            'alternateName' => 'Tra cứu mã số thuế doanh nghiệp, cá nhân',
            'headline' => 'Tra cứu mã số thuế doanh nghiệp & cá nhân chính xác, cập nhật mới nhất',
            "potentialAction" => [
                "@type" => "SearchAction",
                "target" => route('search', ['q' => '{search_term_string}']),
                "query-input" => "required name=search_term_string"
            ],
            "publisher" => [
                "@type" => "Organization",
                "name" => "Thongtin.vn",
                "url" => route('home'),
                "logo" => asset("images/logo/thongtin_light.png"),
                "contactPoint" => [
                    "@type" => "ContactPoint",
                    // "telephone" => "+**************",
                    "contactType" => "customer service"
                ]
            ]
        ]);
        return view('home');
    }

    public function search(Request $request)
    {
        if (empty($request->q)) {
            return redirect()->route('home');
        }

        $tax_businesses = TaxBusiness::search('', function (Client $client, $body) use ($request) {
            $boolQuery = new BoolQuery();
            $boolQuery->add(new MatchPhraseQuery('name', $request->q), 'should');
            $boolQuery->add(new MatchPhraseQuery('tax_id', $request->q), 'should');
            $body->addQuery($boolQuery);
            return $client->search(['body' => $body->toArray()])->asArray();
        })->paginate(10)->withQueryString();

        return view('search', compact('tax_businesses'));
    }

    public function industries()
    {
        SEOMeta::setTitle('Tra cứu mã số thuế doanh nghiệp theo ngành nghề');
        SEOMeta::setDescription('Tra cứu mã số thuế doanh nghiệp theo từng ngành nghề nhanh chóng và chính xác. Cập nhật thông tin mới nhất về MST cho các công ty, hộ kinh doanh trên toàn quốc tại Thongtin.vn');
        JsonLd::setTitle('Tra cứu mã số thuế doanh nghiệp theo ngành nghề');
        JsonLd::setDescription('Tra cứu mã số thuế doanh nghiệp theo từng ngành nghề nhanh chóng và chính xác. Cập nhật thông tin mới nhất về MST cho các công ty, hộ kinh doanh trên toàn quốc tại Thongtin.vn');
        JsonLd::setType('WebPage');
        JsonLd::addValues([
            "headline" => "Tra cứu mã số thuế doanh nghiệp theo ngành nghề",
            "breadcrumb" => [
                "@type" => "BreadcrumbList",
                "itemListElement" => [
                    [
                        "@type" => "ListItem",
                        "position" => 1,
                        "name" => "Trang chủ",
                        "item" => route('home')
                    ],
                    [
                        "@type" => "ListItem",
                        "position" => 2,
                        "name" => "Tra cứu mã số thuế theo ngành nghề",
                        "item" => route('industries')
                    ]
                ]
            ],
            "publisher" => [
                "@type" => "Organization",
                "name" => "Thongtin.vn",
                "url" => route('home'),
                "logo" => asset('images/logo/thongtin_light.png'),
                "contactPoint" => [
                    "@type" => "ContactPoint",
                    "contactType" => "customer service"
                ]
            ]
        ]);
        return view('tax.industries');
    }

    public function searchByIndustry($industryCode)
    {
        $code = last(explode("-", $industryCode));
        $industry = Industry::where('code', $code)->first();
        abort_if(!$industry, 404);

        //        $tax_businesses = DB::table('tax_businesses')
        //            ->select('tax_businesses.*')
        //            ->join('tax_industries', 'tax_businesses.id', '=', 'tax_industries.tax_business_id')
        //            ->join('industries', 'tax_industries.industry_code', '=', 'industries.code')
        //            ->where('industries.code', $code)
        //            ->distinct('tax_businesses.tax_id')
        //            ->paginate(15);

        // $tax_businesses = DB::table('tax_businesses')
        //     ->where('tax_businesses.industries', 'LIKE', '%' . $code . '%') // Lọc doanh nghiệp có ngành nghề phù hợp
        //     ->simplePaginate(15);

        $tax_businesses = TaxBusiness::search('', function (Client $client, $body) use ($code) {
            $body->addQuery(new MatchPhraseQuery('industries', $code));
            return $client->search(['body' => $body->toArray()])->asArray();
        })->paginate(15);

        SEOMeta::setTitle("Tra cứu mã số thuế doanh nghiệp theo ngành {$industry->name}");
        SEOMeta::setDescription("Tra cứu mã số thuế doanh nghiệp theo ngành {$industry->name} nhanh chóng và chính xác. Cập nhật thông tin mới nhất về MST cho các công ty, hộ kinh doanh trên toàn quốc tại Thongtin.vn");
        JsonLd::setTitle("Tra cứu mã số thuế doanh nghiệp theo ngành {$industry->name}");
        JsonLd::setDescription("Tra cứu mã số thuế doanh nghiệp theo ngành {$industry->name} nhanh chóng và chính xác. Cập nhật thông tin mới nhất về MST cho các công ty, hộ kinh doanh trên toàn quốc tại Thongtin.vn");
        JsonLd::setType('WebPage');
        JsonLd::addValues([
            "headline" => "Tra cứu mã số thuế doanh nghiệp theo ngành {$industry->name}",
            "breadcrumb" => [
                "@type" => "BreadcrumbList",
                "itemListElement" => [
                    [
                        "@type" => "ListItem",
                        "position" => 1,
                        "name" => "Trang chủ",
                        "item" => route('home')
                    ],
                    [
                        "@type" => "ListItem",
                        "position" => 2,
                        "name" => "Tra cứu mã số thuế theo ngành nghề",
                        "item" => route('industries')
                    ],
                    [
                        "@type" => "ListItem",
                        "position" => 3,
                        "name" => "Tra cứu mã số thuế theo ngành {$industry->name}",
                        "item" => route('search-by-industry', ['industryCode' => Str::slug($industry->name) . '-' . $industry->code])
                    ]
                ]
            ],
            "publisher" => [
                "@type" => "Organization",
                "name" => "Thongtin.vn",
                "url" => route('home'),
                "logo" => asset('images/logo/thongtin_light.png'),
                "contactPoint" => [
                    "@type" => "ContactPoint",
                    "contactType" => "customer service"
                ]
            ]
        ]);

        return view('tax.search-by-industry', compact('industry', 'tax_businesses'));
    }

    public function provinces()
    {
        SEOMeta::setTitle('Tra cứu mã số thuế theo tỉnh thành phố');
        SEOMeta::setDescription('Tra cứu mã số thuế doanh nghiệp theo tỉnh thành phố nhanh chóng, chính xác. Cập nhật MST mới nhất của công ty, hộ kinh doanh toàn quốc trên Thongtin.vn');
        JsonLd::setTitle('Tra cứu mã số thuế theo tỉnh thành phố');
        JsonLd::setDescription('Tra cứu mã số thuế doanh nghiệp theo tỉnh thành phố nhanh chóng, chính xác. Cập nhật MST mới nhất của công ty, hộ kinh doanh toàn quốc trên Thongtin.vn');
        JsonLd::setType('WebPage');
        JsonLd::addValues([
            "headline" => "Tra cứu mã số thuế theo tỉnh thành phố",
            "breadcrumb" => [
                "@type" => "BreadcrumbList",
                "itemListElement" => [
                    [
                        "@type" => "ListItem",
                        "position" => 1,
                        "name" => "Trang chủ",
                        "item" => route('home')
                    ],
                    [
                        "@type" => "ListItem",
                        "position" => 2,
                        "name" => "Tra cứu mã số thuế theo tỉnh thành phố",
                        "item" => route('provinces')
                    ]
                ]
            ],
            "publisher" => [
                "@type" => "Organization",
                "name" => "Thongtin.vn",
                "url" => route('home'),
                "logo" => asset('images/logo/thongtin_light.png'),
                "contactPoint" => [
                    "@type" => "ContactPoint",
                    "contactType" => "customer service"
                ]
            ]
        ]);

        return view('tax.provinces');
    }

    public function searchByProvince($provinceCode)
    {
        $code = last(explode("-", $provinceCode));
        $province = Province::search()->where('code', $code)->first();
        $district = District::search()->where('code', $code)->first();
        $ward = Ward::search()->where('code', $code)->first();
        abort_if(!$province && !$district && !$ward, 404);

        if ($province) {
            SEOMeta::setTitle("Tra cứu mã số thuế và danh sách công ty tại {$province->full_name}");
            SEOMeta::setDescription("Tra cứu mã số thuế và danh sách công ty tại {$province->full_name} nhanh chóng, chính xác. Cập nhật MST doanh nghiệp mới nhất của công ty, hộ kinh doanh trên Thongtin.vn");
            JsonLd::setTitle("Tra cứu mã số thuế và danh sách công ty tại {$province->full_name}");
            JsonLd::setDescription("Tra cứu mã số thuế và danh sách công ty tại {$province->full_name} nhanh chóng, chính xác. Cập nhật MST doanh nghiệp mới nhất của công ty, hộ kinh doanh trên Thongtin.vn");
            JsonLd::setType('WebPage');
            JsonLd::addValues([
                "headline" => "Tra cứu mã số thuế và danh sách công ty tại {$province->full_name}",
                "breadcrumb" => [
                    "@type" => "BreadcrumbList",
                    "itemListElement" => [
                        [
                            "@type" => "ListItem",
                            "position" => 1,
                            "name" => "Trang chủ",
                            "item" => route('home')
                        ],
                        [
                            "@type" => "ListItem",
                            "position" => 2,
                            "name" => "Tra cứu mã số thuế theo tỉnh thành phố",
                            "item" => route('provinces')
                        ],
                        [
                            "@type" => "ListItem",
                            "position" => 3,
                            "name" => "Tra cứu mã số thuế và danh sách công ty tại {$province->full_name}",
                            "item" => route('search-by-province', ['provinceCode' => Str::slug($province->full_name) . '-' . $province->code])
                        ]
                    ]
                ],
                "publisher" => [
                    "@type" => "Organization",
                    "name" => "Thongtin.vn",
                    "url" => route('home'),
                    "logo" => asset('images/logo/thongtin_light.png'),
                    "contactPoint" => [
                        "@type" => "ContactPoint",
                        "contactType" => "customer service"
                    ]
                ]
            ]);

            $districtIds =  District::where('province_code', $code)->get()->pluck('code')->toArray();
            $wardIds =  Ward::whereIn('district_code', $districtIds)->get()->pluck('code')->toArray();
            $tax_businesses = TaxBusiness::whereIn('ward_code', $wardIds)->orderByDesc('updated_at')->paginate(10);
        }
        if ($district) {
            SEOMeta::setTitle("Tra cứu mã số thuế và danh sách công ty tại {$district->full_name} - {$district->province->full_name}");
            SEOMeta::setDescription("Tra cứu mã số thuế và danh sách công ty tại {$district->full_name} - {$district->province->full_name} nhanh chóng, chính xác. Cập nhật MST doanh nghiệp mới nhất của công ty, hộ kinh doanh trên Thongtin.vn");
            JsonLd::setTitle("Tra cứu mã số thuế và danh sách công ty tại {$district->full_name} - {$district->province->full_name}");
            JsonLd::setDescription("Tra cứu mã số thuế và danh sách công ty tại {$district->full_name} - {$district->province->full_name} nhanh chóng, chính xác. Cập nhật MST doanh nghiệp mới nhất của công ty, hộ kinh doanh trên Thongtin.vn");
            JsonLd::setType('WebPage');
            JsonLd::addValues([
                "headline" => "Tra cứu mã số thuế và danh sách công ty tại {$district->full_name} - {$district->province->full_name}",
                "breadcrumb" => [
                    "@type" => "BreadcrumbList",
                    "itemListElement" => [
                        [
                            "@type" => "ListItem",
                            "position" => 1,
                            "name" => "Trang chủ",
                            "item" => route('home')
                        ],
                        [
                            "@type" => "ListItem",
                            "position" => 2,
                            "name" => "Tra cứu mã số thuế theo tỉnh thành phố",
                            "item" => route('provinces')
                        ],
                        [
                            "@type" => "ListItem",
                            "position" => 3,
                            "name" => "Tra cứu mã số thuế và danh sách công ty tại {$district->province->full_name}",
                            "item" => route('search-by-province', ['provinceCode' => Str::slug($district->province->full_name) . '-' . $district->province->code])
                        ],
                        [
                            "@type" => "ListItem",
                            "position" => 4,
                            "name" => "Tra cứu mã số thuế và danh sách công ty tại {$district->full_name} - {$district->province->full_name}",
                            "item" => route('search-by-province', ['provinceCode' => Str::slug($district->full_name) . '-' . $district->code])
                        ]
                    ]
                ],
                "publisher" => [
                    "@type" => "Organization",
                    "name" => "Thongtin.vn",
                    "url" => route('home'),
                    "logo" => asset('images/logo/thongtin_light.png'),
                    "contactPoint" => [
                        "@type" => "ContactPoint",
                        "contactType" => "customer service"
                    ]
                ]
            ]);
            $wardIds =  Ward::where('district_code', $code)->get()->pluck('code')->toArray();
            $tax_businesses = TaxBusiness::whereIn('ward_code', $wardIds)->orderByDesc('updated_at')->paginate(10);
        }

        if ($ward) {
            SEOMeta::setTitle("Tra cứu mã số thuế và danh sách công ty tại {$ward->full_name} - {$ward->district->full_name} - {$ward->district->province->full_name}");
            SEOMeta::setDescription("Tra cứu mã số thuế và danh sách công ty tại {$ward->full_name} - {$ward->district->full_name} - {$ward->district->province->full_name} nhanh chóng, chính xác. Cập nhật MST doanh nghiệp mới nhất của công ty, hộ kinh doanh trên Thongtin.vn");
            JsonLd::setTitle("Tra cứu mã số thuế và danh sách công ty tại {$ward->full_name} - {$ward->district->full_name} - {$ward->district->province->full_name}");
            JsonLd::setDescription("Tra cứu mã số thuế và danh sách công ty tại {$ward->full_name} - {$ward->district->full_name} - {$ward->district->province->full_name} nhanh chóng, chính xác. Cập nhật MST doanh nghiệp mới nhất của công ty, hộ kinh doanh trên Thongtin.vn");
            JsonLd::setType('WebPage');
            JsonLd::addValues([
                "headline" => "Tra cứu mã số thuế và danh sách công ty tại {$ward->full_name} - {$ward->district->full_name} - {$ward->district->province->full_name}",
                "breadcrumb" => [
                    "@type" => "BreadcrumbList",
                    "itemListElement" => [
                        [
                            "@type" => "ListItem",
                            "position" => 1,
                            "name" => "Trang chủ",
                            "item" => route('home')
                        ],
                        [
                            "@type" => "ListItem",
                            "position" => 2,
                            "name" => "Tra cứu mã số thuế theo tỉnh thành phố",
                            "item" => route('provinces')
                        ],
                        [
                            "@type" => "ListItem",
                            "position" => 3,
                            "name" => "Tra cứu mã số thuế và danh sách công ty tại {$ward->district->province->full_name}",
                            "item" => route('search-by-province', ['provinceCode' => Str::slug($ward->district->province->full_name) . '-' . $ward->district->province->code])
                        ],
                        [
                            "@type" => "ListItem",
                            "position" => 4,
                            "name" => "Tra cứu mã số thuế và danh sách công ty tại {$ward->district->full_name}",
                            "item" => route('search-by-province', ['provinceCode' => Str::slug($ward->district->full_name) . '-' . $ward->district->code])
                        ],
                        [
                            "@type" => "ListItem",
                            "position" => 5,
                            "name" => "Tra cứu mã số thuế và danh sách công ty tại {$ward->full_name} - {$ward->district->full_name} - {$ward->district->province->full_name}",
                            "item" => route('search-by-province', ['provinceCode' => Str::slug($ward->full_name) . '-' . $ward->code])
                        ]
                    ]
                ],
                "publisher" => [
                    "@type" => "Organization",
                    "name" => "Thongtin.vn",
                    "url" => route('home'),
                    "logo" => asset('images/logo/thongtin_light.png'),
                    "contactPoint" => [
                        "@type" => "ContactPoint",
                        "contactType" => "customer service"
                    ]
                ]
            ]);
            $tax_businesses = TaxBusiness::where('ward_code', $code)->orderByDesc('updated_at')->paginate(10);
        }


        return view('tax.search-by-province', compact('province', 'district', 'ward', 'tax_businesses'));
    }

    public function detailTaxBusiness($taxSlug)
    {
        preg_match('/^\d{1,}(?:-\d{1,})?/', $taxSlug, $matches);
        $tax_id = $matches[0] ?? null;
        $business = TaxBusiness::with('relatedTaxes')->where('tax_id', $tax_id)->first();
        abort_if(!$business, 404);

        // Tách các industry_code từ cột industries (giả sử chúng được phân cách bằng dấu phẩy)
        $industryCodes = explode(',', $business->industries);
        $industryCodes = array_filter($industryCodes, function ($value) {
            return !empty($value); // Loại bỏ các giá trị trống
        });

        $industries = Industry::whereIn('code', $industryCodes)->get();




        foreach ($industries as $industry) {
            // So sánh primary_industry với các industry_code và đánh dấu primary = 1
            if (in_array($industry->code, $industryCodes) && $industry->code == $business->primary_industry) {
                $industry->primary = 1;
            } else {
                $industry->primary = 0;
            }
        }

        SEOMeta::setTitle("Mã số thuế {$business->tax_id} - {$business->name}");
        SEOMeta::setDescription("Tra cứu mã số thuế {$business->tax_id} của {$business->name}. Xem thông tin đăng ký kinh doanh, địa chỉ trụ sở, ngành nghề hoạt động và người đại diện pháp luật trên Thongtin.vn");
        JsonLd::setTitle("Mã số thuế {$business->tax_id} - {$business->name}");
        JsonLd::setDescription("Tra cứu mã số thuế {$business->tax_id} của {$business->name}. Xem thông tin đăng ký kinh doanh, địa chỉ trụ sở, ngành nghề hoạt động và người đại diện pháp luật trên Thongtin.vn");
        JsonLd::setType('Corporation');
        JsonLd::addValues([
            "headline" => "Mã số thuế {$business->tax_id} - {$business->name}",
            "identifier" => $business->tax_id,
            "name" => $business->name,
            "legalName" => $business->name,
            "foundingDate" => $business->establish_date,
            "address" => [
                "@type" => "PostalAddress",
                "streetAddress" => $business->address,
                "addressLocality" => $business->ward?->district?->full_name,
                "addressRegion" => $business->ward?->district?->province?->full_name,
                "addressCountry" => "VN",
            ],
            "breadcrumb" => [
                "@type" => "BreadcrumbList",
                "itemListElement" => [
                    [
                        "@type" => "ListItem",
                        "position" => 1,
                        "name" => "Trang chủ",
                        "item" => route('home')
                    ],
                    [
                        "@type" => "ListItem",
                        "position" => 2,
                        "name" => $business->name,
                        "item" => route('detail.business', ['taxSlug' => $business->tax_id . '-' . Str::slug($business->name)])
                    ]
                ]
            ],
            "publisher" => [
                "@type" => "Organization",
                "name" => "Thongtin.vn",
                "url" => route('home'),
                "logo" => asset('images/logo/thongtin_light.png'),
                "contactPoint" => [
                    "@type" => "ContactPoint",
                    "contactType" => "customer service"
                ]
            ]
        ]);

        return view('tax.detail-business', compact('business', 'industries'));
    }

    public function detailTaxPersonal($taxSlug)
    {
        preg_match('/^\d{1,}-?\d{1,}/', $taxSlug, $matches);
        $tax_id = $matches[0] ?? null;
        $personal = TaxPersonal::where('tax_id', $tax_id)->first();
        abort_if(!$personal, 404);

        SEOMeta::setTitle($personal->tax_id . '-' . $personal->name);
        SEOMeta::setDescription('Tra cứu mã số thuế cá nhân ' . $personal->tax_id . ' của ' . $personal->name . '. Cập nhật thông tin MST chính xác, nhanh chóng trên Thongtin.vn. Kiểm tra ngay!');
        JsonLd::setTitle($personal->tax_id . '-' . $personal->name);
        JsonLd::setDescription('Tra cứu mã số thuế cá nhân ' . $personal->tax_id . ' của ' . $personal->name . '. Cập nhật thông tin MST chính xác, nhanh chóng trên Thongtin.vn. Kiểm tra ngay!');
        JsonLd::setType('Person');
        JsonLd::addValues([
            "identifier" => $personal->tax_id,
            "address" => [
                "@type" => "PostalAddress",
                "addressLocality" => "Việt Nam",
            ],
            "headline" => "Tra cứu mã số thuế cá nhân online nhanh & chính xác nhất",
            "breadcrumb" => [
                "@type" => "BreadcrumbList",
                "itemListElement" => [
                    [
                        "@type" => "ListItem",
                        "position" => 1,
                        "name" => "Trang chủ",
                        "item" => route('home')
                    ],
                    [
                        "@type" => "ListItem",
                        "position" => 2,
                        "name" => "Mã số thuế cá nhân",
                        "item" => route('search-tax-personal')
                    ],
                    [
                        "@type" => "ListItem",
                        "position" => 3,
                        "name" => $personal->tax_id . '-' . $personal->name,
                        "item" => route('detail.personal', ['taxSlug' => $personal->tax_id . '-' . Str::slug($personal->name)])
                    ]
                ]
            ],
            "publisher" => [
                "@type" => "Organization",
                "name" => "Thongtin.vn",
                "url" => route('home'),
                "logo" => asset("images/logo/thongtin_light.png"),
                "contactPoint" => [
                    "@type" => "ContactPoint",
                    "contactType" => "customer service"
                ]
            ]
        ]);

        return view('tax.detail-personal', compact('personal'));
    }

    public function searchTaxPersonal()
    {
        SEOMeta::setTitle('Tra cứu mã số thuế cá nhân online - CCCD, CMND, MST nhanh & chính xác');
        SEOMeta::setDescription('Tra cứu mã số thuế cá nhân online nhanh & chính xác bằng CCCD, CMND hoặc MST. Cập nhật mới nhất, kiểm tra miễn phí ngay trên Thongtin.vn!');
        JsonLd::setTitle('Tra cứu mã số thuế cá nhân online - CCCD, CMND, MST nhanh & chính xác');
        JsonLd::setDescription('Tra cứu mã số thuế cá nhân online nhanh & chính xác bằng CCCD, CMND hoặc MST. Cập nhật mới nhất, kiểm tra miễn phí ngay trên Thongtin.vn!');
        JsonLd::setType('WebPage');
        JsonLd::addValues([
            "headline" => "Tra cứu mã số thuế cá nhân online nhanh & chính xác nhất",
            "breadcrumb" => [
                "@type" => "BreadcrumbList",
                "itemListElement" => [
                    [
                        "@type" => "ListItem",
                        "position" => 1,
                        "name" => "Trang chủ",
                        "item" => route('home')
                    ],
                    [
                        "@type" => "ListItem",
                        "position" => 2,
                        "name" => "Mã số thuế cá nhân",
                        "item" => route('search-tax-personal')
                    ]
                ]
            ],
            "publisher" => [
                "@type" => "Organization",
                "name" => "Thongtin.vn",
                "url" => route('home'),
                "logo" => asset("images/logo/thongtin_light.png"),
                "contactPoint" => [
                    "@type" => "ContactPoint",
                    "contactType" => "customer service"
                ]
            ]
        ]);
        return view('tax.search-tax-personal',);
    }

    public function phatNguoi()
    {
        $violations = Violation::with(['vehicle'])->whereRaw('time = (SELECT MAX(time) FROM violations v2 WHERE v2.vehicle_id = violations.vehicle_id)')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        SEOMeta::setTitle('Tra cứu phạt nguội ô tô, xe máy toàn quốc');
        SEOMeta::setDescription('Tra cứu phạt nguội ô tô, xe máy trên toàn quốc chính xác, nhanh chóng. Cập nhật lỗi vi phạm, địa điểm, mức phạt và hướng dẫn nộp phạt mới nhất trên Thongtin.vn');
        JsonLd::setTitle('Tra cứu phạt nguội ô tô, xe máy toàn quốc');
        JsonLd::setDescription('Tra cứu phạt nguội ô tô, xe máy trên toàn quốc chính xác, nhanh chóng. Cập nhật lỗi vi phạm, địa điểm, mức phạt và hướng dẫn nộp phạt mới nhất trên Thongtin.vn');
        JsonLd::setType('WebPage');
        JsonLd::addValues([
            "headline" => "Tra cứu phạt nguội ô tô, xe máy toàn quốc",
            "breadcrumb" => [
                "@type" => "BreadcrumbList",
                "itemListElement" => [
                    [
                        "@type" => "ListItem",
                        "position" => 1,
                        "name" => "Trang chủ",
                        "item" => route('home')
                    ],
                    [
                        "@type" => "ListItem",
                        "position" => 2,
                        "name" => "Tra cứu phạt nguội",
                        "item" => route('phat-nguoi')
                    ]
                ]
            ],
            "publisher" => [
                "@type" => "Organization",
                "name" => "Thongtin.vn",
                "url" => route('home'),
                "logo" => asset("images/logo/thongtin_light.png"),
                "contactPoint" => [
                    "@type" => "ContactPoint",
                    "contactType" => "customer service"
                ]
            ]
        ]);

        return view('phat-nguoi', compact('violations'));
    }

    public function detailPhatNguoi($bks)
    {
        $vehicle = VehicleNumber::with('violations')->where('number', $bks)->first();
        abort_if(!$vehicle, 404);

        $violationJsonLd = [];
        foreach ($vehicle->violations as $violation) {
            $violationJsonLd[] = [
                '@type' => 'Event',
                'name' => $violation->violation,
                'startDate' => $violation->time,
                'location' => [
                    '@type' => 'Place',
                    'name' => $violation->location,
                    'address' => $violation->location,
                ],
            ];
        }

        SEOMeta::setTitle("Tra cứu phạt nguội " . formatVehicleNumber($vehicle->number) . ": Xem lỗi vi phạm & nộp phạt");
        SEOMeta::setDescription("Tra cứu phạt nguội xe " . formatVehicleNumber($vehicle->number) . " nhanh chóng, chính xác. Xem lỗi vi phạm, địa điểm, thời gian vi phạm và hướng dẫn nộp phạt trực tuyến hoặc trực tiếp trên Thongtin.vn");
        JsonLd::setTitle("Tra cứu phạt nguội " . formatVehicleNumber($vehicle->number) . ": Xem lỗi vi phạm & nộp phạt");
        JsonLd::setDescription("Tra cứu phạt nguội xe " . formatVehicleNumber($vehicle->number) . " nhanh chóng, chính xác. Xem lỗi vi phạm, địa điểm, thời gian vi phạm và hướng dẫn nộp phạt trực tuyến hoặc trực tiếp trên Thongtin.vn");
        JsonLd::setType('Corporation');
        JsonLd::addValues([
            "headline" => "Xe " . formatVehicleNumber($vehicle->number) . " có " . count($vehicle->violations) . " phạt nguội",
            "vehicle" => [
                "@type" => "Vehicle",
                "vehicleIdentificationNumber" => formatVehicleNumber($vehicle->number),
                "name" => "Xe " . formatVehicleNumber($vehicle->number),
                "url" => route('detai.phat-nguoi', ['bks' => $vehicle->number]),
                "description" => "Thông tin vi phạm phạt nguội của xe " . formatVehicleNumber($vehicle->number) . ". Xem lỗi vi phạm, thời gian, địa điểm và hướng dẫn nộp phạt.",
                "numberOfViolations" => count($vehicle->violations),
                "violation" => $violationJsonLd,
            ],
            "breadcrumb" => [
                "@type" => "BreadcrumbList",
                "itemListElement" => [
                    [
                        "@type" => "ListItem",
                        "position" => 1,
                        "name" => "Trang chủ",
                        "item" => route('home')
                    ],
                    [
                        "@type" => "ListItem",
                        "position" => 2,
                        "name" => "Tra cứu phạt nguội",
                        "item" => route('phat-nguoi')
                    ],
                    [
                        "@type" => "ListItem",
                        "position" => 3,
                        "name" => "Phạt nguội xe " . formatVehicleNumber($vehicle->number),
                        "item" => route('detai.phat-nguoi', ['bks' => $vehicle->number])
                    ]
                ]
            ],
            "publisher" => [
                "@type" => "Organization",
                "name" => "Thongtin.vn",
                "url" => route('home'),
                "logo" => asset('images/logo/thongtin_light.png'),
                "contactPoint" => [
                    "@type" => "ContactPoint",
                    "contactType" => "customer service"
                ]
            ]
        ]);

        return view('chi-tiet-phat-nguoi', compact('vehicle'));
    }

    public function administrativeUnitsDemo()
    {
        SEOMeta::setTitle('Đơn Vị Hành Chính Việt Nam (Dự kiến)');
        SEOMeta::setDescription('Demo tương tác với đơn vị hành chính Việt Nam - Chọn tỉnh/thành phố để xem danh sách phường/xã tương ứng');
        JsonLd::setTitle('Đơn Vị Hành Chính Việt Nam (Dự kiến)');
        JsonLd::setDescription('Demo tương tác với đơn vị hành chính Việt Nam - Chọn tỉnh/thành phố để xem danh sách phường/xã tương ứng');
        JsonLd::addValues([
            'alternateName' => 'Đơn Vị Hành Chính Việt Nam (Dự kiến)',
            'headline' => 'Demo tương tác với đơn vị hành chính Việt Nam',
            "breadcrumb" => [
                "@type" => "BreadcrumbList",
                "itemListElement" => [
                    [
                        "@type" => "ListItem",
                        "position" => 1,
                        "name" => "Trang chủ",
                        "item" => route('home')
                    ],
                    [
                        "@type" => "ListItem",
                        "position" => 2,
                        "name" => "Demo Đơn Vị Hành Chính",
                        "item" => route('administrative-units-demo')
                    ]
                ]
            ],
            "publisher" => [
                "@type" => "Organization",
                "name" => "Thongtin.vn",
                "url" => route('home'),
                "logo" => asset('images/logo/thongtin_light.png'),
                "contactPoint" => [
                    "@type" => "ContactPoint",
                    "contactType" => "customer service"
                ]
            ]
        ]);

        return view('administrative-units-demo');
    }
}
