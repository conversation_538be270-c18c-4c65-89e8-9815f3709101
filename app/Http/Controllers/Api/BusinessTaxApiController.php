<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Industry;
use App\Models\TaxBusiness;
use App\Models\Ward;
use App\Models\WardV2;
use App\Services\CrawlTaxInfoFromTCTService;
use Illuminate\Http\Request;

class BusinessTaxApiController extends Controller
{
    protected $crawlTaxInfoFromTCTService;

    public function __construct()
    {
        $this->crawlTaxInfoFromTCTService = new CrawlTaxInfoFromTCTService();
    }

    public function get(Request $request, $tax_id)
    {
        $business = TaxBusiness::where('tax_id', $tax_id)->first();
        if (!$business) {
            $info = $this->crawlTaxInfoFromTCTService->handle($tax_id);
            if ($info && isset($info['taxid'])) {
                $dataTax = [
                    'tax_id' => $info['taxid'],
                    'name' => $info['official_name'],
                    'alternate_name' => $info['official_name'],
                    'short_alternate_name' => $info['trading_name'],
                    'address' => $info['head_office_address'],
                    'representative_name' => $info['legal_representative'],
                    'id_number' => $info['legal_representative_id'],
                    'phone' => $info['phone'],
                    'establish_date' => now()->parse(str_replace("/", "-", $info['start_date']))->format('Y-m-d'),
                    'management_agency' => $info['tax_management_office'],
                    'company_type' => null,
                    'status' => $info['note'],
                    'director_name' => $info['director_name'] ?? null,
                    'chief_accountant' => $info['chief_accountant'] ?? null,
                ];

                if ($info['industries'] && !empty($info['industries'])) {
                    $industries = '';
                    $primary_industries = null;
                    foreach ($info['industries'] as $industry) {
                        $industries .= $industry['code'] . ',';
                        if ($industry['primary'] == 'Y') {
                            $primary_industries = $industry['code'];
                        }
                    }
                    $dataTax['industries'] = $industries;
                    $dataTax['primary_industry'] = $primary_industries;
                }


                $addressSplit = explode(',', $dataTax['address']);
                $provinceName = $addressSplit[count($addressSplit) - 1] ?? null;
                $wardName = $addressSplit[count($addressSplit) - 3] ?? null;
                if ($wardName) {
                    $formatWardName = preg_replace_callback('/\d+/', function ($matches) {
                        return str_pad($matches[0], 2, '0', STR_PAD_LEFT);
                    }, $wardName);
                    $ward = WardV2::where(function ($query) use ($formatWardName, $wardName) {
                        $query->where('name', 'like', '%' . trim($wardName) . '%')
                            ->orWhere('name', 'like', '%' . trim($formatWardName) . '%');
                    })
                        ->whereHas('province', function ($query) use ($provinceName) {
                            $query->where('name', 'like', '%' . trim(str_replace("TP", "", $provinceName)) . '%');
                        })->first();
                    if ($ward) {
                        $dataTax['ward_code'] = $ward->ward_code;
                    }
                }
                $newTax = TaxBusiness::where('tax_id', $dataTax['tax_id'])->first();
                if (!$newTax) {
                    $newTax = TaxBusiness::create($dataTax);
                }
                $data = $newTax->only([
                    'tax_id',
                    'name',
                    'alternate_name',
                    'short_alternate_name',
                    'address',
                    'establish_date',
                    'management_agency',
                    'status',
                ]);

                $industryCodes = explode(',', $newTax->industries);
                $industryCodes = array_filter($industryCodes, function ($value) {
                    return !empty($value); // Loại bỏ các giá trị trống
                });

                $industries = Industry::select('id', 'name', 'code')->whereIn('code', $industryCodes)->get();
                foreach ($industries as $industry) {
                    // So sánh primary_industry với các industry_code và đánh dấu primary = 1
                    if (in_array($industry->code, $industryCodes) && $industry->code == $newTax->primary_industry) {
                        $industry->primary = true;
                    } else {
                        $industry->primary = false;
                    }
                }
                $data['industries'] = $industries;

                return response()->json(['status' => true, 'data' => $data]);
            } else {
                return response()->json(['status' => false, 'msg' => 'NNT Không trùng khớp với tìm kiếm của bạn.']);
            }
        }

        $data = $business->only([
            'tax_id',
            'name',
            'alternate_name',
            'short_alternate_name',
            'representative_name',
            'address',
            'establish_date',
            'management_agency',
            'status',
        ]);

        $industryCodes = explode(',', $business->industries);
        $industryCodes = array_filter($industryCodes, function ($value) {
            return !empty($value); // Loại bỏ các giá trị trống
        });

        $industries = Industry::select('id', 'name', 'code')->whereIn('code', $industryCodes)->get();
        foreach ($industries as $industry) {
            // So sánh primary_industry với các industry_code và đánh dấu primary = 1
            if (in_array($industry->code, $industryCodes) && $industry->code == $business->primary_industry) {
                $industry->primary = true;
            } else {
                $industry->primary = false;
            }
        }
        $data['industries'] = $industries;

        return response()->json(['status' => true, 'data' => $data]);
    }
}
