<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\WardMapping;
use Illuminate\Http\Request;

class AddressConversionController extends Controller
{
    public function conversion(Request $request)
    {
        $validated = $request->validate([
            'address' => 'required',
        ]);
        $addressSplit = explode(',', $validated['address']);
        $provinceName = $addressSplit[count($addressSplit) - 1] ?? null;
        $districtName = $addressSplit[count($addressSplit) - 2] ?? null;
        $wardName = $addressSplit[count($addressSplit) - 3] ?? null;
        if (!$wardName) {
            return response()->json(['status' => false, 'message' => 'Không tìm thấy địa chỉ mới']);
        }
        $detailAddress = implode(',', array_slice($addressSplit, 0, -3));


        $newAddress = WardMapping::where('old_ward_name', 'like', '%' . trim($wardName) . '%')
            ->where('old_district_name', 'like', '%' . trim($districtName) . '%')
            ->where('old_province_name', 'like', '%' . trim($provinceName) . '%')
            ->first();
        if ($newAddress) {
            return response()->json([
                'status' => true,
                'new_address' => !empty($detailAddress) ? $detailAddress . ', ' . $newAddress->new_ward_name . ', ' . $newAddress->new_province_name
                    : $newAddress->new_ward_name . ', ' . $newAddress->new_province_name,
            ]);
        }
        return response()->json(['status' => false, 'message' => 'Không tìm thấy địa chỉ mới']);
    }
}
