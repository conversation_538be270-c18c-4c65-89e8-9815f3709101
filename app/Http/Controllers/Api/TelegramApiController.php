<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\TaxBusiness;
use App\Models\TaxPersonal;
use App\Models\TelegramChatState;
use App\Models\VehicleNumber;
use App\Models\Ward;
use App\Models\WardV2;
use App\Services\CrawlCSGTService;
use App\Services\CrawlTaxInfoFromTCTService;
use App\Services\CrawlTaxPersonalFromTCTService;
use Elastic\Elasticsearch\Client;
use Http\Discovery\Psr17Factory;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MatchPhraseQuery;
use Symfony\Bridge\PsrHttpMessage\Factory\PsrHttpFactory;
use Telegram\Bot\Laravel\Facades\Telegram;

class TelegramApiController extends Controller
{
    public function webhook(Request $request)
    {
        Log::debug($request->all());
        Log::debug(file_get_contents('php://input'));
        Log::debug(json_decode(file_get_contents('php://input'), true));

        $psr17Factory = new Psr17Factory();
        $psrHttpFactory = new PsrHttpFactory($psr17Factory, $psr17Factory, $psr17Factory, $psr17Factory);
        $psrRequest = $psrHttpFactory->createRequest($request);

        $telegram = Telegram::bot('mybot');
        $updates = $telegram->getWebhookUpdate(true, $psrRequest);

        $message = $updates->getMessage();
        Log::debug($updates);

        if (!empty($message) && !$message->isEmpty()) {
            try {
                Log::debug($message);
                $chatId = $message->getChat()->getId();
                $chatType = $message->getChat()->getType();
                $fromId = $message->getFrom()->getId();
                // $chatState = TelegramChatState::where('chat_id', $chatId)->first();
                $chatState = TelegramChatState::where('chat_id', $chatId)->where('from_id', $fromId)->first();
                if ($chatState) {
                    $moreInfoText = "\n\n<i>Để tra cứu thông tin khác, bạn có thể sử dụng 📝 các lệnh cơ bản:</i>\n";
                    $moreInfoText .= "/mstdoanhnghiep <i>- Tra cứu mã số thuế doanh nghiệp</i>\n";
                    $moreInfoText .= "/mstcanhan <i>- Tra cứu mã số thuế cá nhân</i>\n";
                    $moreInfoText .= "/phatnguoi <i>- Tra cứu phạt nguội biển số xe</i>\n\n";
                    $moreInfoText .= "🔄 Chia sẻ BOT cho bạn bè:\n";
                    $moreInfoText .= "<a href='https://t.me/thongtinvn_bot?start=help_'>https://t.me/thongtinvn_bot</a> - BOT tra cứu thông tin\n";
                    $moreInfoText .= "<a href='https://t.me/thongtinvn_group'>https://t.me/thongtinvn_group</a> - Group cộng đồng thongtin.vn\n";
                    $moreInfoText .= "<a href='https://thongtin.vn'>https://thongtin.vn</a> - Trang web tra cứu thông tin\n";
                    if (str_starts_with($message->getText(), "/")) {
                        $telegram->commandsHandler(true, $psrRequest);
                        return response()->json(['status' => true]);
                    } else {
                        $replyMessage = $telegram->sendMessage([
                            'chat_id' => $chatId,
                            'reply_to_message_id' => $message->getMessageId(),
                            'text' => '⚙️ Đang tìm kiếm thông tin...'
                        ]);
                        if ($chatState->search_type == 'mstdoanhnghiep') {
                            $items = TaxBusiness::search('', function (Client $client, $body) use ($message) {
                                $boolQuery = new BoolQuery();
                                $boolQuery->add(new MatchPhraseQuery('name', $message->getText()), 'should');
                                $boolQuery->add(new MatchPhraseQuery('tax_id', $message->getText()), 'should');
                                $body->addQuery($boolQuery);
                                return $client->search(['body' => $body->toArray()])->asArray();
                            })->get();
                            if (count($items) > 1) {
                                $replyText = "Kết quả tra cứu: " . $message->getText() . " có " . count($items) . " doanh nghiệp\n";
                                $replyText .= "Xem danh sách <a href='" . route('search', ['q' => $message->getText()]) . "'>tại đây</a>";
                                $telegram->editMessageText([
                                    'parse_mode' => 'HTML',
                                    'chat_id' => $chatId,
                                    'message_id' => $replyMessage->getMessageId(),
                                    'text' =>  $replyText . $moreInfoText,
                                    'reply_to_message_id' => $message->getMessageId()
                                ]);
                                if ($chatType == 'group' || $chatType == 'supergroup') {
                                    TelegramChatState::where('chat_id', $chatId)->where('from_id', $fromId)->delete();
                                }
                                return response()->json(['status' => true]);
                            }
                            if (count($items)  == 1) {
                                $item = $items->first();
                                $replyText = "✅ <strong>Mã số thuế:</strong> {$item->tax_id}\n";
                                $replyText .= "✅ <strong>Tên doanh nghiệp:</strong> {$item->name}\n";
                                $replyText .= "✅ <strong>Người đại diện:</strong> {$item->representative_name}\n";
                                $replyText .= "✅ <strong>Địa chỉ:</strong> {$item->address}\n";
                                $replyText .= "✅ <strong>Ngày hoạt động:</strong> {$item->establish_date}\n";
                                $replyText .= "✅ <strong>Quản lý bởi:</strong> {$item->management_agency}\n";
                                $replyText .= "✅ <strong>Tình trạng:</strong> {$item->status}";
                                $telegram->editMessageText([
                                    'parse_mode' => 'HTML',
                                    'chat_id' => $chatId,
                                    'message_id' => $replyMessage->getMessageId(),
                                    'text' => $replyText . $moreInfoText,
                                    'reply_to_message_id' => $message->getMessageId()
                                ]);
                                if ($chatType == 'group' || $chatType == 'supergroup') {
                                    TelegramChatState::where('chat_id', $chatId)->where('from_id', $fromId)->delete();
                                }
                                return response()->json(['status' => true]);
                            }
                            $crawlTaxInfoFromTCTService = new CrawlTaxInfoFromTCTService();

                            $info = $crawlTaxInfoFromTCTService->handle($message->getText());
                            if ($info && isset($info['taxid'])) {
                                $dataTax = [
                                    'tax_id' => $info['taxid'],
                                    'name' => $info['official_name'],
                                    'alternate_name' => $info['official_name'],
                                    'short_alternate_name' => $info['trading_name'],
                                    'address' => $info['head_office_address'],
                                    'representative_name' => $info['legal_representative'],
                                    'id_number' => $info['legal_representative_id'],
                                    'phone' => $info['phone'],
                                    'establish_date' => now()->parse(str_replace("/", "-", $info['start_date']))->format('Y-m-d'),
                                    'management_agency' => $info['tax_management_office'],
                                    'company_type' => null,
                                    'status' => $info['note'],
                                    'director_name' => $info['director_name'] ?? null,
                                    'chief_accountant' => $info['chief_accountant'] ?? null,
                                ];

                                if ($info['industries'] && !empty($info['industries'])) {
                                    $industries = '';
                                    $primary_industries = null;
                                    foreach ($info['industries'] as $industry) {
                                        $industries .= $industry['code'] . ',';
                                        if ($industry['primary'] == 'Y') {
                                            $primary_industries = $industry['code'];
                                        }
                                    }
                                    $dataTax['industries'] = $industries;
                                    $dataTax['primary_industry'] = $primary_industries;
                                }


                                $addressSplit = explode(',', $dataTax['address']);
                                $provinceName = $addressSplit[count($addressSplit) - 1] ?? null;
                                $wardName = $addressSplit[count($addressSplit) - 3] ?? null;
                                if ($wardName) {
                                    $formatWardName = preg_replace_callback('/\d+/', function ($matches) {
                                        return str_pad($matches[0], 2, '0', STR_PAD_LEFT);
                                    }, $wardName);
                                    $ward = WardV2::where(function ($query) use ($formatWardName, $wardName) {
                                        $query->where('name', 'like', '%' . trim($wardName) . '%')
                                            ->orWhere('name', 'like', '%' . trim($formatWardName) . '%');
                                    })
                                        ->whereHas('province', function ($query) use ($provinceName) {
                                            $query->where('name', 'like', '%' . trim(str_replace("TP", "", $provinceName)) . '%');
                                        })->first();
                                    if ($ward) {
                                        $dataTax['ward_code'] = $ward->ward_code;
                                    }
                                }
                                $newTax = TaxBusiness::where('tax_id', $dataTax['tax_id'])->first();
                                if (!$newTax) {
                                    $newTax = TaxBusiness::create($dataTax);
                                }
                                $replyText = "✅ <strong>Mã số thuế:</strong> {$newTax->tax_id}\n";
                                $replyText .= "✅ <strong>Tên doanh nghiệp:</strong> {$newTax->name}\n";
                                $replyText .= "✅ <strong>Người đại diện:</strong> {$newTax->representative_name}\n";
                                $replyText .= "✅ <strong>Địa chỉ:</strong> {$newTax->address}\n";
                                $replyText .= "✅ <strong>Ngày hoạt động:</strong> {$newTax->establish_date}\n";
                                $replyText .= "✅ <strong>Quản lý bởi:</strong> {$newTax->management_agency}\n";
                                $replyText .= "✅ <strong>Tình trạng:</strong> {$newTax->status}";
                                $telegram->editMessageText([
                                    'parse_mode' => 'HTML',
                                    'chat_id' => $chatId,
                                    'message_id' => $replyMessage->getMessageId(),
                                    'text' => $replyText . $moreInfoText,
                                    'reply_to_message_id' => $message->getMessageId()
                                ]);
                                if ($chatType == 'group' || $chatType == 'supergroup') {
                                    TelegramChatState::where('chat_id', $chatId)->where('from_id', $fromId)->delete();
                                }
                                return response()->json(['status' => true]);
                            }
                            $telegram->editMessageText([
                                'parse_mode' => 'HTML',
                                'chat_id' => $chatId,
                                'message_id' => $replyMessage->getMessageId(),
                                'text' => "NNT Không trùng khớp với tìm kiếm của bạn." . $moreInfoText,
                                'reply_to_message_id' => $message->getMessageId()
                            ]);
                            if ($chatType == 'group' || $chatType == 'supergroup') {
                                TelegramChatState::where('chat_id', $chatId)->where('from_id', $fromId)->delete();
                            }
                            return response()->json(['status' => true]);
                        }
                        if ($chatState->search_type == 'mstcanhan') {
                            $item = TaxPersonal::where('tax_id', $message->getText())
                                ->orWhere('id_number', $message->getText())
                                ->first();

                            if ($item) {
                                $replyText = "✅ <strong>Mã số thuế:</strong> {$item->tax_id}\n";
                                $replyText .= "✅ <strong>Họ và tên:</strong> {$item->name}\n";
                                $replyText .= "✅ <strong>CCCD/CMT:</strong> {$item->id_number}\n";
                                $replyText .= "✅ <strong>Địa chỉ:</strong> {$item->address}\n";
                                $replyText .= "✅ <strong>Nơi đăng ký quản lý:</strong> {$item->tax_management_office}\n";
                                $replyText .= "✅ <strong>Tình trạng:</strong> {$item->status}\n";
                                $replyText .= "✅ <strong>Ngày cấp MST:</strong> {$item->issue_date}\n";
                                $replyText .= "✅ <strong>Ngày đóng MST:</strong> {$item->close_date}";
                                $telegram->editMessageText([
                                    'parse_mode' => 'HTML',
                                    'chat_id' => $chatId,
                                    'message_id' => $replyMessage->getMessageId(),
                                    'text' => $replyText . $moreInfoText,
                                    'reply_to_message_id' => $message->getMessageId()
                                ]);
                                if ($chatType == 'group' || $chatType == 'supergroup') {
                                    TelegramChatState::where('chat_id', $chatId)->where('from_id', $fromId)->delete();
                                }
                                return response()->json(['status' => true]);
                            } else {
                                $crawlTaxPersonalFromTCTService = new CrawlTaxPersonalFromTCTService();
                                $info = $crawlTaxPersonalFromTCTService->handle($message->getText());
                                if ($info && isset($info['taxid'])) {
                                    $dataTax = [
                                        'tax_id' => $info['taxid'],
                                        'name' => $info['name'],
                                        'id_number' => $info['id_number'],
                                        'address' => $info['district'] . ' - ' . $info['province'],
                                        'phone' => $info['phone'],
                                        'issue_date' => !empty($info['issue_date']) ? now()->parse($info['issue_date'])->format('Y-m-d') : null,
                                        'close_date' => $info['close_date'] ? now()->parse($info['close_date'])->format('Y-m-d') : null,
                                        'tax_management_office' => $info['tax_management_office'],
                                        'head_office_address' => $info['head_office_address'],
                                        'status' => $info['note'],
                                    ];
                                    $newTax = TaxPersonal::create($dataTax);
                                    $replyText = "✅ <strong>Mã số thuế:</strong> {$newTax->tax_id}\n";
                                    $replyText .= "✅ <strong>Họ và tên:</strong> {$newTax->name}\n";
                                    $replyText .= "✅ <strong>CCCD/CMT:</strong> {$newTax->id_number}\n";
                                    $replyText .= "✅ <strong>Địa chỉ:</strong> {$newTax->address}\n";
                                    $replyText .= "✅ <strong>Ngày cấp MST:</strong> {$newTax->establish_date}\n";
                                    $replyText .= "✅ <strong>Nơi đăng ký quản lý:</strong> {$newTax->tax_management_office}\n";
                                    $replyText .= "✅ <strong>Tình trạng:</strong> {$newTax->status}";
                                    $telegram->editMessageText([
                                        'parse_mode' => 'HTML',
                                        'chat_id' => $chatId,
                                        'message_id' => $replyMessage->getMessageId(),
                                        'text' => $replyText . $moreInfoText,
                                        'reply_to_message_id' => $message->getMessageId()
                                    ]);
                                    if ($chatType == 'group' || $chatType == 'supergroup') {
                                        TelegramChatState::where('chat_id', $chatId)->where('from_id', $fromId)->delete();
                                    }
                                    return response()->json(['status' => true]);
                                }
                                $telegram->editMessageText([
                                    'parse_mode' => 'HTML',
                                    'chat_id' => $chatId,
                                    'message_id' => $replyMessage->getMessageId(),
                                    'text' => "NNT Không trùng khớp với tìm kiếm của bạn." . $moreInfoText,
                                    'reply_to_message_id' => $message->getMessageId()
                                ]);
                                if ($chatType == 'group' || $chatType == 'supergroup') {
                                    TelegramChatState::where('chat_id', $chatId)->where('from_id', $fromId)->delete();
                                }
                                return response()->json(['status' => true]);
                            }
                        }
                        if ($chatState->search_type == 'phatnguoi') {

                            $service = new CrawlCSGTService();

                            $result = $service->phatnguoi(trim($message->getText()));
                            if ($result && isset($result['status']) && $result['status']) {
                                $vehicle = VehicleNumber::with('violations')->where('id', $result['vehicle_id'])->first();
                                $replyText = "Biển số xe {$vehicle->number} có " . count($vehicle->violations) . " phạt nguội\n\n";
                                foreach ($vehicle->violations as $key => $violation) {
                                    $replyText .= "<strong>📝 VI PHẠM " . ($key + 1) . "</strong>\n";
                                    $replyText .= "<strong>🕒 Thời gian:</strong> " . now()->parse($violation->time)->format('H:i, d-m-Y') . "\n";
                                    $replyText .= "<strong>📍 Địa điểm:</strong> {$violation->location}\n";
                                    $replyText .= "<strong>❗️ Hành vi:</strong> {$violation->violation}\n";
                                    $replyText .= "<strong>⚖️ Trạng thái:</strong> {$violation->status}\n\n";
                                }
                                $telegram->editMessageText([
                                    'parse_mode' => 'HTML',
                                    'chat_id' => $chatId,
                                    'message_id' => $replyMessage->getMessageId(),
                                    'text' => $replyText . $moreInfoText,
                                    'reply_to_message_id' => $message->getMessageId()
                                ]);
                                if ($chatType == 'group' || $chatType == 'supergroup') {
                                    TelegramChatState::where('chat_id', $chatId)->where('from_id', $fromId)->delete();
                                }
                                return response()->json(['status' => true]);
                            }
                            if ($result && isset($result['status']) && $result['status'] === false) {
                                if (isset($result['empty']) && $result['empty']) {
                                    $telegram->editMessageText([
                                        'parse_mode' => 'HTML',
                                        'message_id' => $replyMessage->getMessageId(),
                                        'chat_id' => $chatId,
                                        'text' => "Không tìm thấy phạt nguội trên biển số xe " . $message->getText()  . $moreInfoText,
                                        'reply_to_message_id' => $message->getMessageId()
                                    ]);
                                    if ($chatType == 'group' || $chatType == 'supergroup') {
                                        TelegramChatState::where('chat_id', $chatId)->where('from_id', $fromId)->delete();
                                    }
                                    return response()->json(['status' => true]);
                                } else {
                                    $telegram->editMessageText([
                                        'parse_mode' => 'HTML',
                                        'chat_id' => $chatId,
                                        'message_id' => $replyMessage->getMessageId(),
                                        'text' => "Lỗi hệ thống, vui lòng thử lại." . $moreInfoText,
                                        'reply_to_message_id' => $message->getMessageId()
                                    ]);
                                    if ($chatType == 'group' || $chatType == 'supergroup') {
                                        TelegramChatState::where('chat_id', $chatId)->where('from_id', $fromId)->delete();
                                    }
                                    return response()->json(['status' => true]);
                                }
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                Log::info("====Error TelegramApiController====");
                Log::error($e);
                $replyMessage = $telegram->sendMessage([
                    'chat_id' => $chatId,
                    'text' => '❗️ Đã có lỗi xảy ra, vui lòng thử lại.'
                ]);
                TelegramChatState::where('chat_id', $chatId)->where('from_id', $fromId)->delete();
                return response()->json(['status' => true]);
            }
        }

        $telegram->commandsHandler(true, $psrRequest);
        Log::info("end======");
        return response()->json(['status' => true]);
    }
}
