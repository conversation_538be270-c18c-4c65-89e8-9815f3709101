<?php

namespace App\Telegram\Commands;

use App\Models\TelegramChatState;
use Telegram\Bot\Commands\Command;
use Telegram\Bot\Keyboard\Keyboard;

class PhatNguoiCommand extends Command
{
    protected string $name = 'phatnguoi';
    protected string $description = 'Tra cứu phạt nguội biển số xe';

    public function handle()
    {

        $this->replyWithMessage([
            'text' => "Nhập vào biển số xe cần tra cứu. VD: 30A12345",
            'reply_to_message_id' => $this->getUpdate()->getMessage()->getMessageId()
        ]);
        $chatId = $this->getUpdate()->getChat()->id;
        $fromId = $this->getUpdate()->getMessage()->getFrom()->id;
        TelegramChatState::where('chat_id', $chatId)->where('from_id', $fromId)->delete();
        TelegramChatState::create([
            'chat_id' => $chatId,
            'from_id' => $fromId,
            'search_type' => $this->getName()
        ]);
    }
}
