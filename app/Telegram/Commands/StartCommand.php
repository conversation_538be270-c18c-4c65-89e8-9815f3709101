<?php

namespace App\Telegram\Commands;

use App\Models\TelegramChatState;
use Illuminate\Support\Facades\Log;
use Telegram\Bot\Commands\Command;
use Telegram\Bot\Keyboard\Keyboard;

class StartCommand extends Command
{
    protected string $name = 'start';
    protected string $description = 'Khởi động BOT và xem hướng dẫn';

    public function handle()
    {
        $textReply = "👋 Chào mừng bạn đến với BOT Thongtin.vn - tra cứu mã số thuế doanh nghiệp, cá nhân, phạt nguội\n\n";
        $textReply .= "🤖 Bot giúp bạn:\n";
        $textReply .= "• Tra cứu mã số thuế doanh nghiệp\n";
        $textReply .= "• Tra cứu mã số thuế cá nhân\n";
        $textReply .= "• Tra cứu phạt nguội biển số xe\n\n";
        $textReply .= "📝 <PERSON>ác l<PERSON>nh cơ bản:\n";
        $textReply .= "/mstdoanhnghiep - Tra cứu mã số thuế doanh nghiệp\n";
        $textReply .= "/mstcanhan - Tra cứu mã số thuế cá nhân\n";
        $textReply .= "/phatnguoi - Tra cứu phạt nguội biển số xe\n\n";
        $textReply .= "🔄 Chia sẻ BOT cho bạn bè:\n";
        $textReply .= "<a href='https://t.me/thongtinvn_bot?start=help_'>https://t.me/thongtinvn_bot</a> - BOT tra cứu thông tin\n";
        $textReply .= "<a href='https://t.me/thongtinvn_group'>https://t.me/thongtinvn_group</a> - Group cộng đồng thongtin.vn\n";
        $textReply .= "<a href='https://thongtin.vn'>https://thongtin.vn</a> - Trang web tra cứu thông tin\n";
        Log::debug($textReply);
        Log::debug($this->getUpdate());
        Log::debug($this->getUpdate()->getMessage());
        $this->replyWithMessage([
            'text' => $textReply,
            'parse_mode' => 'HTML',
            'reply_to_message_id' => $this->getUpdate()->getMessage()->getMessageId()
        ]);

        $chatId = $this->getUpdate()->getChat()->id;
        $fromId = $this->getUpdate()->getMessage()->getFrom()->id;
        TelegramChatState::where('chat_id', $chatId)->where('from_id', $fromId)->delete();
    }
}
