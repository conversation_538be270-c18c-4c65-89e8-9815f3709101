<?php

namespace App\Telegram\Commands;

use App\Models\TelegramChatState;
use Telegram\Bot\Commands\Command;
use Telegram\Bot\Keyboard\Keyboard;

class MSTCaNhanCommand extends Command
{
    protected string $name = 'mst<PERSON>han';
    protected string $description = 'Tra cứu mã số thuế cá nhân';

    public function handle()
    {

        $this->replyWithMessage([
            'text' => "Nhập vào mã số thuế hoặc số Căn cước công dân/Chứng minh thư",
            'reply_to_message_id' => $this->getUpdate()->getMessage()->getMessageId()
        ]);
        $chatId = $this->getUpdate()->getChat()->id;
        $fromId = $this->getUpdate()->getMessage()->getFrom()->id;
        TelegramChatState::where('chat_id', $chatId)->where('from_id', $fromId)->delete();
        TelegramChatState::create([
            'chat_id' => $chatId,
            'from_id' => $fromId,
            'search_type' => $this->getName()
        ]);
    }
}
