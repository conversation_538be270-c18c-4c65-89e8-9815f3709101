<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AddressConversion extends Model
{
    protected $fillable = [
        'ward_name',
        'district_name',
        'province_name',
        'detail_address',
        'new_detail',
        'new_ward',
        'new_ward_code',
        'new_province',
        'new_province_code',
        'new_full_address',
        'not_sure',
        'success',
        'ward_code_missing',
        'province_code_missing'
    ];

    protected $casts = [
        'not_sure' => 'boolean',
        'success' => 'boolean',
        'ward_code_missing' => 'boolean',
        'province_code_missing' => 'boolean',
    ];
}
