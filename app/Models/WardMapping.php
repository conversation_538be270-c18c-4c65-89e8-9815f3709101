<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WardMapping extends Model
{
    protected $table = 'ward_mappings';
    protected $fillable = [
        'old_ward_code',
        'old_ward_name',
        'old_district_name',
        'old_province_name',
        'new_ward_code',
        'new_ward_name',
        'new_province_name',
    ];
    public function oldWard()
    {
        return $this->belongsTo(OldWard::class, 'old_ward_code', 'code');
    }

    public function newWard()
    {
        return $this->belongsTo(WardV2::class, 'new_ward_code', 'ward_code');
    }
}
