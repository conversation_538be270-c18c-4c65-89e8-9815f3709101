<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BusinessProfile extends Model
{
    protected $table = 'business_profiles';
    protected $fillable = [

        'taxid',
        'official_name',
        'date_of_issue',
        'date_of_tax_closure',
        'trading_name',
        'tax_management_office',
        'head_office_address',
        'tax_registration_office',
        'tax_notification_address',
        'decision_date',
        'issuing_authority',
        'business_license',
        'issue_date',
        'declaration_receipt_date',
        'fiscal_year_start',
        'fiscal_year_end',
        'current_taxid',
        'start_date',
        'program_section',
        'accounting_method',
        'vat_calculation_method',
        'legal_representative',
        'legal_representative_address',
        'legal_representative_id',
        'director_name',
        'director_address',
        'chief_accountant',
        'note',
        'data',
        'industry',
        'dvtructhuoc',
        'vpdd',
        'dvthanhvien',
        'tax_type',
        'dnchuquan'

    ];

    protected $casts = [
        'data' => 'json',
        'tax_type' => 'json'
    ];
}
