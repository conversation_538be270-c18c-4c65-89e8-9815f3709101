<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Violation extends Model
{
    protected $fillable = [
        'vehicle_id',
        'time',
        'location',
        'violation',
        'status',
        'detection_unit',
        'place_of_solution',
        'color',
    ];

    protected $casts = [
        'place_of_solution' => 'json',
    ];

    public function vehicle()
    {
        return $this->belongsTo(VehicleNumber::class, 'vehicle_id', 'id');
    }
}
