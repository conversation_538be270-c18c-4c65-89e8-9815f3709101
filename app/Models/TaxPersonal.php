<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TaxPersonal extends Model
{
    protected $fillable = [
        'tax_id',
        'name',
        'id_number',
        'address',
        'phone',
        'issue_date',
        'close_date',
        'tax_management_office',
        'head_office_address',
        'status',
    ];

    public function relatedTaxes()
    {
        return $this->hasMany(TaxBusiness::class, 'id_number', 'id_number');
    }
}
