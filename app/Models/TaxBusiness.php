<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use <PERSON><PERSON>\Scout\Searchable;

class TaxBusiness extends Model
{
    use Searchable;
    protected $fillable = [
        'tax_id',
        'name',
        'alternate_name',
        'short_alternate_name',
        'address',
        'ward_code',
        'representative_name',
        'phone',
        'establish_date',
        'management_agency',
        'id_number',
        'company_type',
        'primary_industry',
        'industries',
        'status',
    ];

    public function ward()
    {
        return $this->belongsTo(Ward::class, 'ward_code', 'code');
    }

    public function tax_industries()
    {
        return $this->hasMany(TaxIndustry::class, 'tax_business_id', 'id');
    }

    public function primaryIndustry()
    {
        return $this->belongsToMany(Industry::class, 'tax_industries', 'tax_business_id', 'industry_code', 'id', 'code')
            ->where('primary', true);
    }

    public function relatedTaxes()
    {
        return $this->hasMany(TaxBusiness::class, 'id_number', 'id_number');
    }


    public function getSlugAttribute()
    {
        return Str::slug($this->name); // Tạo slug từ trường name
    }

    public function getFullUrlAttribute()
    {
        return url("ma-so-thue-doanh-nghiep/{$this->tax_id}-{$this->slug}");
    }
}
