<?php

namespace App\Console\Commands;

use App\Services\AddressConversionService;
use Illuminate\Console\Command;

class ProcessDvhcDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dvhc:process 
                            {search? : Search term to look for in DVHC data}
                            {--type=all : Type of search (province, district, ward, all)}
                            {--create : Create AddressConversion records from search results}
                            {--stats : Show DVHC data statistics}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process DVHC data to search for old administrative unit names and create AddressConversion records';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $addressConversionService = new AddressConversionService();

        try {
            // Show statistics if requested
            if ($this->option('stats')) {
                $this->showStatistics($addressConversionService);
                return;
            }

            $searchTerm = $this->argument('search');
            
            if (!$searchTerm) {
                $this->error('Please provide a search term or use --stats option');
                return;
            }

            $type = $this->option('type');
            $this->info("Searching for '{$searchTerm}' in DVHC data (type: {$type})...");

            // Search in DVHC data
            $results = $addressConversionService->searchInDvhc($searchTerm, $type);

            if (empty($results)) {
                $this->warn("No results found for '{$searchTerm}'");
                return;
            }

            $this->info("Found " . count($results) . " results:");

            // Display results
            $this->displayResults($results);

            // Create AddressConversion records if requested
            if ($this->option('create')) {
                $this->info("\nCreating AddressConversion records...");
                
                $creationResults = $addressConversionService->createAddressConversions($results, $searchTerm);
                
                $this->info("Created: " . $creationResults['total_created'] . " records");
                
                if ($creationResults['total_errors'] > 0) {
                    $this->warn("Errors: " . $creationResults['total_errors']);
                    
                    foreach ($creationResults['errors'] as $error) {
                        $this->error("Error: " . $error['error']);
                    }
                }

                // Show sample created records
                if (!empty($creationResults['created'])) {
                    $this->info("\nSample created records:");
                    $sample = array_slice($creationResults['created'], 0, 3);
                    
                    foreach ($sample as $record) {
                        $this->line("- {$record->ward_name}, {$record->district_name}, {$record->province_name}");
                        $this->line("  → {$record->new_ward} ({$record->new_ward_code}), {$record->new_province} ({$record->new_province_code})");
                        $this->line("  Success: " . ($record->success ? 'Yes' : 'No'));
                        $this->line("");
                    }
                }
            } else {
                $this->info("\nUse --create option to create AddressConversion records from these results.");
            }

        } catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
        }
    }

    /**
     * Display search results
     */
    private function displayResults($results)
    {
        foreach ($results as $index => $result) {
            $this->line("Result " . ($index + 1) . ":");
            
            if (isset($result['types'])) {
                // Multiple type matches
                $this->line("  Types: " . implode(', ', $result['types']));
                $this->line("  Province: " . $result['province']);
                $this->line("  District: " . $result['district']);
                $this->line("  Wards: " . $result['wards']);
            } else {
                // Single type match
                $this->line("  Type: " . $result['type']);
                
                switch ($result['type']) {
                    case 'province':
                        $this->line("  Province: " . $result['old_name']);
                        $this->line("  District: " . $result['district']);
                        $this->line("  Wards: " . $result['wards']);
                        break;
                    
                    case 'district':
                        $this->line("  Province: " . $result['province']);
                        $this->line("  District: " . $result['old_name']);
                        $this->line("  Wards: " . $result['wards']);
                        break;
                    
                    case 'ward':
                        $this->line("  Province: " . $result['province']);
                        $this->line("  District: " . $result['district']);
                        $this->line("  Ward: " . $result['old_name']);
                        $this->line("  All Wards: " . $result['all_wards']);
                        break;
                }
            }
            
            $this->line("");
        }
    }

    /**
     * Show DVHC data statistics
     */
    private function showStatistics($addressConversionService)
    {
        $this->info("DVHC Data Statistics:");
        $this->line("");
        
        $stats = $addressConversionService->getStatistics();
        
        $this->line("Total rows: " . number_format($stats['total_rows']));
        $this->line("Unique provinces: " . number_format($stats['unique_provinces']));
        $this->line("Unique districts: " . number_format($stats['unique_districts']));
        $this->line("Estimated total wards: " . number_format($stats['estimated_total_wards']));
        
        $this->line("");
        $this->info("Provinces found in DVHC data:");
        
        foreach ($stats['provinces_list'] as $province) {
            $this->line("- " . $province);
        }
    }
}
