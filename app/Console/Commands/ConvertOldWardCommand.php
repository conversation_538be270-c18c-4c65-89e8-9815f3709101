<?php

namespace App\Console\Commands;

use App\Jobs\ConvertOldWardToNewWardJob;
use App\Models\AddressConversion;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Queue;

class ConvertOldWardCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'address:convert-old-ward 
                            {--batch-size=100 : Number of records to process per batch}
                            {--queue=default : Queue name to use for jobs}
                            {--sync : Run synchronously instead of using queue}
                            {--preview=10 : Number of sample records to preview before processing}
                            {--force : Skip confirmation prompt}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Convert old ward information in AddressConversion to new ward data using DVHC mapping';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 Converting Old Ward to New Ward Data...');
        $this->line('');

        try {
            // Check if DVHC file exists
            $this->checkDvhcFile();

            // Get total records to process
            $totalRecords = AddressConversion::count();
            
            if ($totalRecords === 0) {
                $this->warn('No AddressConversion records found to process.');
                return;
            }

            $this->info("📊 Total AddressConversion records: " . number_format($totalRecords));

            // Preview sample records if requested
            $previewCount = $this->option('preview');
            if ($previewCount > 0) {
                $this->previewSampleRecords($previewCount);
            }

            // Get processing options
            $batchSize = $this->option('batch-size');
            $queueName = $this->option('queue');
            $runSync = $this->option('sync');
            $force = $this->option('force');

            $totalBatches = ceil($totalRecords / $batchSize);

            $this->info("⚙️  Processing Configuration:");
            $this->line("   Batch size: " . number_format($batchSize));
            $this->line("   Total batches: " . number_format($totalBatches));
            $this->line("   Queue: " . ($runSync ? 'Synchronous' : $queueName));
            $this->line('');

            // Confirmation
            if (!$force && !$this->confirm('Do you want to proceed with the conversion?')) {
                $this->info('Operation cancelled.');
                return;
            }

            // Dispatch jobs
            $this->info('🚀 Dispatching conversion jobs...');
            
            $jobsDispatched = 0;
            
            for ($offset = 0; $offset < $totalRecords; $offset += $batchSize) {
                $job = new ConvertOldWardToNewWardJob($batchSize, $offset);
                
                if ($runSync) {
                    $job->handle();
                } else {
                    Queue::connection()->pushOn($queueName, $job);
                }
                
                $jobsDispatched++;
                
                if ($jobsDispatched % 10 === 0) {
                    $this->line("   Dispatched {$jobsDispatched}/{$totalBatches} batches...");
                }
            }

            $this->info("✅ Successfully dispatched {$jobsDispatched} conversion jobs!");
            
            if (!$runSync) {
                $this->line('');
                $this->info('📋 Monitor job progress:');
                $this->line('   php artisan queue:work --queue=' . $queueName);
                $this->line('   php artisan queue:failed');
                $this->line('');
                $this->info('📊 Check conversion results:');
                $this->line('   php artisan address:conversion-stats');
            } else {
                $this->line('');
                $this->info('📊 Synchronous processing completed!');
                $this->showConversionStats();
            }

        } catch (\Exception $e) {
            $this->error('❌ Error: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
        }
    }

    /**
     * Check if DVHC file exists and is valid
     */
    private function checkDvhcFile()
    {
        $dvhcPath = database_path('data/dvhc.json');
        
        if (!file_exists($dvhcPath)) {
            throw new \Exception('DVHC JSON file not found at: ' . $dvhcPath);
        }

        $this->info('✅ DVHC file found: ' . $dvhcPath);
        
        // Quick validation
        $content = file_get_contents($dvhcPath);
        $data = json_decode($content, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception('Invalid JSON in DVHC file: ' . json_last_error_msg());
        }

        $rowCount = count($data['rows'] ?? []);
        $this->line("   📊 DVHC records: " . number_format($rowCount));
        $this->line('');
    }

    /**
     * Preview sample records
     */
    private function previewSampleRecords($count)
    {
        $this->info("👀 Preview of {$count} sample AddressConversion records:");
        
        $samples = AddressConversion::take($count)->get();
        
        foreach ($samples as $index => $record) {
            $this->line("Sample " . ($index + 1) . ":");
            $this->line("   ID: {$record->id}");
            $this->line("   Old: {$record->ward_name}, {$record->district_name}, {$record->province_name}");
            $this->line("   Current New: {$record->new_ward} ({$record->new_ward_code}), {$record->new_province} ({$record->new_province_code})");
            $this->line("   Status: " . ($record->success ? 'Success' : 'Failed') . ($record->not_sure ? ' (Uncertain)' : ''));
            $this->line('');
        }
    }

    /**
     * Show conversion statistics
     */
    private function showConversionStats()
    {
        $total = AddressConversion::count();
        $successful = AddressConversion::where('success', true)->count();
        $uncertain = AddressConversion::where('not_sure', true)->count();
        $withWardCode = AddressConversion::whereNotNull('new_ward_code')->count();
        $withProvinceCode = AddressConversion::whereNotNull('new_province_code')->count();

        $this->info('📈 Conversion Statistics:');
        $this->table(
            ['Metric', 'Count', 'Percentage'],
            [
                ['Total Records', number_format($total), '100%'],
                ['Successful Conversions', number_format($successful), round(($successful / $total) * 100, 2) . '%'],
                ['Uncertain Conversions', number_format($uncertain), round(($uncertain / $total) * 100, 2) . '%'],
                ['With Ward Code', number_format($withWardCode), round(($withWardCode / $total) * 100, 2) . '%'],
                ['With Province Code', number_format($withProvinceCode), round(($withProvinceCode / $total) * 100, 2) . '%'],
            ]
        );
    }
}
