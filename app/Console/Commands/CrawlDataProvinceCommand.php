<?php

namespace App\Console\Commands;

use App\Models\District;
use App\Models\Province;
use App\Models\Ward;
use App\Services\MaSoThueService;
use DOMDocument;
use DOMXPath;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class CrawlDataProvinceCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:crawl-data-province-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $mstService = new MaSoThueService();
        $result = $mstService->call('/tra-cuu-ma-so-thue-theo-tinh');

        $dom = new DOMDocument();
        @$dom->loadHTML($result);
        $xpath = new DOMXPath($dom);

        // Tìm tất cả các thẻ <a>
        $nodes = $xpath->query("//table[@class='table']//tr/td/a");

        $provinces = [];

        foreach ($nodes as $node) {
            $name = trim($node->nodeValue); // Lấy tên tỉnh/thành phố
            preg_match('/-(\d+)$/', $node->getAttribute('href'), $matches);
            $number = $matches[1] ?? null; // Lấy số cuối URL
            $provinces[] = ['full_name' => $name, 'code' => $number];
        }
        if ($provinces) {
            Province::insert($provinces);
            foreach ($provinces as $province) {
                $result = $mstService->call('/tra-cuu-ma-so-thue-theo-tinh/' . Str::slug($province['full_name']) . '-' . $province['code']);

                $dom = new DOMDocument();
                @$dom->loadHTML($result);
                $xpath = new DOMXPath($dom);

                // Tìm tất cả các thẻ <a>
                $nodes = $xpath->query("//ul[@class='row']/li/a");

                $districts = [];

                foreach ($nodes as $node) {
                    $name = trim($node->nodeValue); // Lấy tên huyện/thành phố
                    preg_match('/-(\d+)$/', $node->getAttribute('href'), $matches);
                    $number = $matches[1] ?? null; // Lấy số cuối URL
                    $districts[] = ['full_name' => $name, 'code' => $number, 'province_code' => $province['code']];
                }
                if ($districts) {
                    District::insert($districts);

                    foreach ($districts as $district) {
                        $result = $mstService->call('/tra-cuu-ma-so-thue-theo-tinh/' . Str::slug($district['full_name']) . '-' . $district['code']);

                        $dom = new DOMDocument();
                        @$dom->loadHTML($result);
                        $xpath = new DOMXPath($dom);

                        // Tìm tất cả các thẻ <a>
                        $nodes = $xpath->query("//ul[@class='row']/li/a");

                        $wards = [];

                        foreach ($nodes as $node) {
                            $name = trim($node->nodeValue); // Lấy tên huyện/thành phố
                            preg_match('/-(\d+)$/', $node->getAttribute('href'), $matches);
                            $number = $matches[1] ?? null; // Lấy số cuối URL
                            $wards[] = ['full_name' => $name, 'code' => $number, 'district_code' => $district['code']];
                        }
                        if ($wards) {
                            Ward::insert($wards);
                        }
                    }
                }
            }
        }
    }
}
