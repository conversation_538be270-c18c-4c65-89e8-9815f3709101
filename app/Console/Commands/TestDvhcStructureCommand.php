<?php

namespace App\Console\Commands;

use App\Models\AddressConversion;
use Illuminate\Console\Command;

class TestDvhcStructureCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'address:test-dvhc 
                            {--sample=5 : Number of DVHC records to show}
                            {--test-conversion : Test conversion logic with sample data}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test and analyze DVHC structure and conversion logic';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Testing DVHC Structure and Conversion Logic');
        $this->line('');

        try {
            // Test 1: Check DVHC file structure
            $this->info('1. Checking DVHC file structure...');
            $dvhcData = $this->loadAndAnalyzeDvhc();
            
            // Test 2: Show sample DVHC records
            $sampleCount = $this->option('sample');
            $this->showSampleDvhcRecords($dvhcData, $sampleCount);
            
            // Test 3: Test conversion logic if requested
            if ($this->option('test-conversion')) {
                $this->testConversionLogic($dvhcData);
            }
            
            $this->info('✅ DVHC structure test completed!');
            
        } catch (\Exception $e) {
            $this->error('❌ Error: ' . $e->getMessage());
        }
    }

    /**
     * Load and analyze DVHC data
     */
    private function loadAndAnalyzeDvhc()
    {
        $dvhcPath = database_path('data/dvhc.json');
        
        if (!file_exists($dvhcPath)) {
            throw new \Exception('DVHC JSON file not found at: ' . $dvhcPath);
        }

        $this->line('   ✅ DVHC file found: ' . $dvhcPath);
        
        $content = file_get_contents($dvhcPath);
        $data = json_decode($content, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception('Invalid JSON in DVHC file: ' . json_last_error_msg());
        }

        $rowCount = count($data['rows'] ?? []);
        $this->line("   📊 Total DVHC records: " . number_format($rowCount));
        
        // Analyze structure
        if (!empty($data['rows'])) {
            $firstRow = $data['rows'][0];
            $columnCount = count($firstRow['columns'] ?? []);
            $this->line("   📋 Columns per record: {$columnCount}");
            $this->line("   📝 Expected structure:");
            $this->line("      Column 0: Tỉnh/TP sau sáp nhập");
            $this->line("      Column 1: Được sáp nhập từ");
            $this->line("      Column 2: New ward");
            $this->line("      Column 3: Các phường/xã cũ");
        }
        
        $this->line('');
        return $data;
    }

    /**
     * Show sample DVHC records
     */
    private function showSampleDvhcRecords($dvhcData, $count)
    {
        $this->info("2. Sample DVHC Records ({$count} samples):");
        
        $samples = array_slice($dvhcData['rows'], 0, $count);
        
        foreach ($samples as $index => $row) {
            $columns = $row['columns'];
            
            $this->line("Sample " . ($index + 1) . ":");
            $this->line("   Tỉnh/TP sau sáp nhập: " . ($columns[0] ?? 'N/A'));
            $this->line("   Được sáp nhập từ: " . ($columns[1] ?? 'N/A'));
            $this->line("   New ward: " . ($columns[2] ?? 'N/A'));
            $this->line("   Các phường/xã cũ: " . ($columns[3] ?? 'N/A'));
            
            // Count old wards
            $oldWards = $columns[3] ?? '';
            $oldWardCount = count(array_filter(array_map('trim', explode(',', $oldWards))));
            $this->line("   📊 Old ward count: {$oldWardCount}");
            $this->line('');
        }
    }

    /**
     * Test conversion logic with sample data
     */
    private function testConversionLogic($dvhcData)
    {
        $this->info('3. Testing conversion logic...');
        
        // Get some sample AddressConversion records
        $sampleConversions = AddressConversion::take(3)->get();
        
        if ($sampleConversions->isEmpty()) {
            $this->warn('   No AddressConversion records found for testing.');
            return;
        }
        
        foreach ($sampleConversions as $index => $conversion) {
            $this->line("Test " . ($index + 1) . " (ID: {$conversion->id}):");
            $this->line("   Old ward: {$conversion->ward_name}");
            
            // Search for this ward in DVHC data
            $match = $this->findOldWardInDvhc($conversion->ward_name, $dvhcData);
            
            if ($match) {
                $this->line("   ✅ Found in DVHC:");
                $this->line("      New Province: {$match['new_province']}");
                $this->line("      New Ward: {$match['new_ward']}");
                $this->line("      Merged From: {$match['merged_from']}");
                $this->line("      Matched Old Ward: {$match['matched_old_ward']}");
            } else {
                $this->line("   ❌ Not found in DVHC data");
            }
            
            $this->line('');
        }
    }

    /**
     * Find old ward in DVHC data (simplified version for testing)
     */
    private function findOldWardInDvhc($oldWardName, $dvhcData)
    {
        $normalizedSearchWard = $this->normalizeWardName($oldWardName);
        
        foreach ($dvhcData['rows'] as $row) {
            $columns = $row['columns'];
            
            $newProvince = $columns[0] ?? '';
            $mergedFrom = $columns[1] ?? '';
            $newWard = $columns[2] ?? '';
            $oldWards = $columns[3] ?? '';

            // Parse old ward names
            $oldWardList = array_map('trim', explode(',', $oldWards));

            foreach ($oldWardList as $oldWard) {
                $normalizedOldWard = $this->normalizeWardName($oldWard);
                
                if ($this->isMatchingWard($normalizedSearchWard, $normalizedOldWard)) {
                    return [
                        'new_province' => $newProvince,
                        'merged_from' => $mergedFrom,
                        'new_ward' => $newWard,
                        'matched_old_ward' => $oldWard,
                        'all_old_wards' => $oldWards
                    ];
                }
            }
        }

        return null;
    }

    /**
     * Normalize ward name for comparison
     */
    private function normalizeWardName($wardName)
    {
        $wardName = mb_strtolower(trim($wardName), 'UTF-8');
        
        // Remove common prefixes
        $prefixes = ['xã ', 'thị trấn ', 'phường ', 'thị xã ', 'thành phố '];
        
        foreach ($prefixes as $prefix) {
            if (mb_strpos($wardName, $prefix, 0, 'UTF-8') === 0) {
                $wardName = mb_substr($wardName, mb_strlen($prefix, 'UTF-8'), null, 'UTF-8');
                break;
            }
        }
        
        return trim($wardName);
    }

    /**
     * Check if two ward names match
     */
    private function isMatchingWard($wardName1, $wardName2)
    {
        // Exact match
        if ($wardName1 === $wardName2) {
            return true;
        }

        // Contains match
        if (mb_strpos($wardName1, $wardName2, 0, 'UTF-8') !== false || 
            mb_strpos($wardName2, $wardName1, 0, 'UTF-8') !== false) {
            return true;
        }

        // Similarity match (80% threshold)
        similar_text($wardName1, $wardName2, $percent);
        return ($percent / 100) >= 0.8;
    }
}
