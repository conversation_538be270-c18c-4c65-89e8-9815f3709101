<?php

namespace App\Console\Commands;

use App\Models\Industry;
use DOMDocument;
use DOMXPath;
use GuzzleHttp\Client;
use Illuminate\Console\Command;

class SyncIndustryCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-industry-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $industryCodes = Industry::all()->pluck('code')->toArray();
        $newIndustries = [];
        $currentPage = 1;
        do {
            $result = $this->getDataByPage($currentPage);
            if (isset($result['data'])) {
                $newIndustries = [
                    ...$newIndustries,
                    ...$result['data']
                ];
            }
            $currentPage++;
        } while (isset($result['data']) && !empty($result['data']));

        foreach ($newIndustries as $key => $item) {
            if (in_array($item['code'], $industryCodes)) {
                unset($newIndustries[$key]);
            }
        }

        if (!empty($newIndustries)) {
            Industry::insert($newIndustries);
        }
    }

    protected function getDataByPage($page = 1)
    {
        $client = new Client();
        $result = $client->get('https://masothue.com/tra-cuu-ma-so-thue-theo-nganh-nghe', [
            'headers' => [
                "authority" => "masothue.com",
                "method" => "GET",
                "path" => "\/tra-cuu-ma-so-thue-theo-nganh-nghe/",
                "scheme" => "https",
                "accept" => "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                // "accept-encoding" => "gzip, deflate, br, zstd",
                "accept-language" => "vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7",
                "cache-control" => "no-cache",
                "cookie" => "res=1680x1050; _ga=GA1.1.811615436.1730879196; __gads=ID=156f22aad563eead:T=1730879196:RT=1733209145:S=ALNI_MYsQlyBuVv_nzUHymLg_K6Pb7YLvQ; __gpi=UID=00000f623b9bf38f:T=1730879196:RT=1733209145:S=ALNI_Mb38zYIYi0xgsBuDQHh17sq3QO6HQ; __eoi=ID=76f09ae1d941773d:T=1730879196:RT=1733209145:S=AA-AfjYUTudvywn2uKZ0eJpNOu1Q; FCNEC=%5B%5B%22AKsRol_vaOgWcWp8kZxn9d0uBcOI4rxHcSjJebJisf1_OBgq5Z4jmfmsxDrwXXyUHxfH6SrfVwzS-vGMP42Bb4Tf89TndwhhkR1fG7qQojyrtW3zWt8SkNYbLgqORBAiefWoaMfv03rxXagXLUes-sMoxqNmZcFLyA%3D%3D%22%5D%5D; PHPSESSID=tsnvmf4ea7knjpsmmtl6ebmn81; _ga_T2DNQ9W5ZK=GS1.1.1739590974.17.1.1739594168.60.0.0",
                "priority" => "u=0, i",
                "referer" => "https://masothue.com",
                "sec-ch-ua" => '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
                "user-agent" => "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36"
            ],
            'query' => [
                'page' => $page,
            ]
        ]);
        $htmlContent = $result->getBody()->getContents();
        $dom = new DOMDocument();
        @$dom->loadHTML($htmlContent);
        $xpath = new DOMXPath($dom);
        $table = $xpath->query("//table[contains(@class, 'table')]")->item(0);

        if (!$table) {
            return false;
        }
        $data = [];
        foreach ($table->getElementsByTagName('tr') as $row) {
            $cells = $row->getElementsByTagName('td');

            if ($cells->length == 2) {
                $code = trim($cells->item(0)->textContent);
                $name = trim($cells->item(1)->textContent);
                $data[] = [
                    'code' => $code,
                    'name' => $name,
                ];
            }
        }
        // Lấy số trang hiện tại
        $currentPageNode = $xpath->query("//span[contains(@class, 'current')]");
        $currentPage = $currentPageNode->length > 0 ? $currentPageNode->item(0)->nodeValue : null;

        // Lấy danh sách các trang tiếp theo
        $nextPages = [];
        $pageNodes = $xpath->query("//a[contains(@class, 'page-numbers')]");

        foreach ($pageNodes as $node) {
            $nextPages[] = [
                'page' => $node->nodeValue,
                'url' => $node->getAttribute('href')
            ];
        }
        return [
            'data' => $data,
            'currentPage' => $currentPage,
            'nextPages' => $nextPages,
        ];
    }
}
