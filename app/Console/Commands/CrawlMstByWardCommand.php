<?php

namespace App\Console\Commands;

use App\Jobs\CrawlByWardJob;
use App\Models\Ward;
use App\Services\MaSoThueService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CrawlMstByWardCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:crawl-mst-by-ward-command {provinceCode}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $wards = Ward::whereHas('district', function ($query) {
            $query->whereHas('province', function ($q) {
                $q->where('code', $this->argument('provinceCode'));
            });
        })->get()->toArray();
        // $wards = Ward::whereNotNull('full_name')->limit(3)->get()->toArray();
        if ($wards) {
            $time = now();
            foreach ($wards as $ward) {
                $slug = Str::slug($ward['full_name']) . "-" . $ward['code'];
                $dataWard = [
                    'code' => $ward['code'],
                    'name' => $ward['full_name'],
                    'url' => "/tra-cuu-ma-so-thue-theo-tinh/$slug",
                ];
                $time = $time->addSeconds(10);
                CrawlByWardJob::dispatch($dataWard)->delay($time);
            }
        }
    }
}
