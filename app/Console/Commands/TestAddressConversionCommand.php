<?php

namespace App\Console\Commands;

use App\Services\AddressConversionService;
use Illuminate\Console\Command;

class TestAddressConversionCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:address-conversion';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the address conversion functionality with sample data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Address Conversion Service...');
        $this->line('');

        try {
            $service = new AddressConversionService();

            // Test 1: Show statistics
            $this->info('1. Testing statistics...');
            $stats = $service->getStatistics();
            $this->line("   Total rows: " . number_format($stats['total_rows']));
            $this->line("   Unique provinces: " . number_format($stats['unique_provinces']));
            $this->line("   Unique districts: " . number_format($stats['unique_districts']));
            $this->line("   Estimated total wards: " . number_format($stats['estimated_total_wards']));
            $this->line('');

            // Test 2: Search for a common province
            $this->info('2. Testing search for "An Giang"...');
            $results = $service->searchInDvhc('An Giang', 'province');
            $this->line("   Found " . count($results) . " results");
            
            if (!empty($results)) {
                $sample = array_slice($results, 0, 2);
                foreach ($sample as $index => $result) {
                    $this->line("   Result " . ($index + 1) . ": {$result['old_name']} - {$result['district']}");
                }
            }
            $this->line('');

            // Test 3: Search for a district
            $this->info('3. Testing search for "Châu Thành"...');
            $results = $service->searchInDvhc('Châu Thành', 'district');
            $this->line("   Found " . count($results) . " results");
            
            if (!empty($results)) {
                $sample = array_slice($results, 0, 2);
                foreach ($sample as $index => $result) {
                    $this->line("   Result " . ($index + 1) . ": {$result['province']} - {$result['old_name']}");
                }
            }
            $this->line('');

            // Test 4: Search for a ward
            $this->info('4. Testing search for "Thị trấn"...');
            $results = $service->searchInDvhc('Thị trấn', 'ward');
            $this->line("   Found " . count($results) . " results");
            
            if (!empty($results)) {
                $sample = array_slice($results, 0, 2);
                foreach ($sample as $index => $result) {
                    $this->line("   Result " . ($index + 1) . ": {$result['province']} - {$result['district']} - {$result['old_name']}");
                }
            }
            $this->line('');

            // Test 5: Create address conversions (limited sample)
            if ($this->confirm('Do you want to test creating AddressConversion records? (This will create actual database records)')) {
                $this->info('5. Testing address conversion creation...');
                
                // Use a small sample for testing
                $testResults = $service->searchInDvhc('An Biên', 'district');
                $testResults = array_slice($testResults, 0, 1); // Limit to 1 result for testing
                
                if (!empty($testResults)) {
                    $creationResults = $service->createAddressConversions($testResults, 'An Biên');
                    
                    $this->line("   Created: " . $creationResults['total_created'] . " records");
                    $this->line("   Errors: " . $creationResults['total_errors']);
                    
                    if (!empty($creationResults['created'])) {
                        $record = $creationResults['created'][0];
                        $this->line("   Sample record:");
                        $this->line("     Old: {$record->ward_name}, {$record->district_name}, {$record->province_name}");
                        $this->line("     New: {$record->new_ward} ({$record->new_ward_code}), {$record->new_province} ({$record->new_province_code})");
                        $this->line("     Success: " . ($record->success ? 'Yes' : 'No'));
                    }
                } else {
                    $this->warn('   No test results found for "An Biên"');
                }
            }

            $this->line('');
            $this->info('✅ Address Conversion Service test completed successfully!');

        } catch (\Exception $e) {
            $this->error('❌ Test failed: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
        }
    }
}
