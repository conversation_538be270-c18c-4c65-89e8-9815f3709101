<?php

namespace App\Console\Commands;

use App\Models\AddressConversion;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AddressConversionStatsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'address:conversion-stats 
                            {--detailed : Show detailed breakdown}
                            {--sample=10 : Number of sample records to show}
                            {--failures : Show only failed conversions}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Show statistics and analysis of AddressConversion records';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('📊 AddressConversion Statistics');
        $this->line('');

        try {
            // Basic statistics
            $this->showBasicStats();

            // Detailed breakdown if requested
            if ($this->option('detailed')) {
                $this->showDetailedStats();
            }

            // Show sample records
            $sampleCount = $this->option('sample');
            if ($sampleCount > 0) {
                if ($this->option('failures')) {
                    $this->showFailedSamples($sampleCount);
                } else {
                    $this->showSampleRecords($sampleCount);
                }
            }

        } catch (\Exception $e) {
            $this->error('❌ Error: ' . $e->getMessage());
        }
    }

    /**
     * Show basic statistics
     */
    private function showBasicStats()
    {
        $total = AddressConversion::count();
        
        if ($total === 0) {
            $this->warn('No AddressConversion records found.');
            return;
        }

        $successful = AddressConversion::where('success', true)->count();
        $failed = AddressConversion::where('success', false)->count();
        $uncertain = AddressConversion::where('not_sure', true)->count();
        $withWardCode = AddressConversion::whereNotNull('new_ward_code')->where('new_ward_code', '!=', '')->count();
        $withProvinceCode = AddressConversion::whereNotNull('new_province_code')->where('new_province_code', '!=', '')->count();
        $withNewWard = AddressConversion::whereNotNull('new_ward')->where('new_ward', '!=', '')->count();

        $this->info('📈 Overall Statistics:');
        $this->table(
            ['Metric', 'Count', 'Percentage'],
            [
                ['Total Records', number_format($total), '100%'],
                ['Successful Conversions', number_format($successful), round(($successful / $total) * 100, 2) . '%'],
                ['Failed Conversions', number_format($failed), round(($failed / $total) * 100, 2) . '%'],
                ['Uncertain Conversions', number_format($uncertain), round(($uncertain / $total) * 100, 2) . '%'],
                ['With New Ward', number_format($withNewWard), round(($withNewWard / $total) * 100, 2) . '%'],
                ['With Ward Code', number_format($withWardCode), round(($withWardCode / $total) * 100, 2) . '%'],
                ['With Province Code', number_format($withProvinceCode), round(($withProvinceCode / $total) * 100, 2) . '%'],
            ]
        );
        $this->line('');
    }

    /**
     * Show detailed statistics breakdown
     */
    private function showDetailedStats()
    {
        $this->info('🔍 Detailed Breakdown:');

        // Status combinations
        $statusBreakdown = DB::table('address_conversions')
            ->select(
                DB::raw('success'),
                DB::raw('not_sure'),
                DB::raw('ward_code_missing'),
                DB::raw('province_code_missing'),
                DB::raw('COUNT(*) as count')
            )
            ->groupBy('success', 'not_sure', 'ward_code_missing', 'province_code_missing')
            ->orderBy('count', 'desc')
            ->get();

        $this->line('Status Combinations:');
        foreach ($statusBreakdown as $status) {
            $description = $this->getStatusDescription($status);
            $this->line("   {$description}: " . number_format($status->count));
        }
        $this->line('');

        // Top provinces by conversion success
        $provinceStats = DB::table('address_conversions')
            ->select(
                'province_name',
                DB::raw('COUNT(*) as total'),
                DB::raw('SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful'),
                DB::raw('ROUND(AVG(CASE WHEN success = 1 THEN 100 ELSE 0 END), 2) as success_rate')
            )
            ->groupBy('province_name')
            ->orderBy('total', 'desc')
            ->limit(10)
            ->get();

        $this->info('🏛️  Top Provinces by Record Count:');
        $this->table(
            ['Province', 'Total', 'Successful', 'Success Rate'],
            $provinceStats->map(function($stat) {
                return [
                    $stat->province_name,
                    number_format($stat->total),
                    number_format($stat->successful),
                    $stat->success_rate . '%'
                ];
            })->toArray()
        );
        $this->line('');
    }

    /**
     * Show sample records
     */
    private function showSampleRecords($count)
    {
        $this->info("📋 Sample Records ({$count} random samples):");
        
        $samples = AddressConversion::inRandomOrder()->take($count)->get();
        
        foreach ($samples as $index => $record) {
            $this->line("Sample " . ($index + 1) . " (ID: {$record->id}):");
            $this->line("   Old: {$record->ward_name}, {$record->district_name}, {$record->province_name}");
            $this->line("   New: {$record->new_ward} ({$record->new_ward_code}), {$record->new_province} ({$record->new_province_code})");
            $this->line("   Status: " . $this->getRecordStatus($record));
            $this->line("   Updated: " . $record->updated_at->diffForHumans());
            $this->line('');
        }
    }

    /**
     * Show failed conversion samples
     */
    private function showFailedSamples($count)
    {
        $this->info("❌ Failed Conversion Samples ({$count} samples):");
        
        $failures = AddressConversion::where('success', false)
            ->orWhere('not_sure', true)
            ->take($count)
            ->get();

        if ($failures->isEmpty()) {
            $this->line('   No failed conversions found!');
            return;
        }
        
        foreach ($failures as $index => $record) {
            $this->line("Failure " . ($index + 1) . " (ID: {$record->id}):");
            $this->line("   Old: {$record->ward_name}, {$record->district_name}, {$record->province_name}");
            $this->line("   New: {$record->new_ward} ({$record->new_ward_code}), {$record->new_province} ({$record->new_province_code})");
            $this->line("   Issues: " . $this->getFailureReasons($record));
            $this->line('');
        }
    }

    /**
     * Get status description for status combination
     */
    private function getStatusDescription($status)
    {
        $parts = [];
        
        if ($status->success) {
            $parts[] = 'Success';
        } else {
            $parts[] = 'Failed';
        }
        
        if ($status->not_sure) {
            $parts[] = 'Uncertain';
        }
        
        if ($status->ward_code_missing) {
            $parts[] = 'No Ward Code';
        }
        
        if ($status->province_code_missing) {
            $parts[] = 'No Province Code';
        }
        
        return implode(', ', $parts);
    }

    /**
     * Get record status description
     */
    private function getRecordStatus($record)
    {
        $status = [];
        
        if ($record->success) {
            $status[] = '✅ Success';
        } else {
            $status[] = '❌ Failed';
        }
        
        if ($record->not_sure) {
            $status[] = '⚠️ Uncertain';
        }
        
        return implode(' ', $status);
    }

    /**
     * Get failure reasons for a record
     */
    private function getFailureReasons($record)
    {
        $reasons = [];
        
        if ($record->ward_code_missing) {
            $reasons[] = 'Ward not found';
        }
        
        if ($record->province_code_missing) {
            $reasons[] = 'Province not found';
        }
        
        if ($record->not_sure) {
            $reasons[] = 'Uncertain mapping';
        }
        
        if (!$record->success) {
            $reasons[] = 'Conversion failed';
        }
        
        return implode(', ', $reasons) ?: 'Unknown';
    }
}
