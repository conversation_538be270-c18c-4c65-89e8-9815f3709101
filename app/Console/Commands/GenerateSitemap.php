<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\SitemapIndex;
use Spatie\Sitemap\Tags\Url;
use App\Models\TaxBusiness;
use Illuminate\Support\Str;

use Carbon\Carbon;

class GenerateSitemap extends Command
{
    protected $signature = 'sitemap:generate';
    protected $description = 'Generate sitemap for tax businesses in chunks';

    public function handle()
    {
        $chunkSize = 40000;
        $fileIndex = 1;
        $sitemapFiles = [];

        TaxBusiness::select(['tax_id', 'name'])
            ->orderBy('id')
            ->chunk($chunkSize, function ($businesses) use (&$fileIndex, &$sitemapFiles) {
                $sitemap = Sitemap::create();

                foreach ($businesses as $business) {
                    $url = url("ma-so-thue-doanh-nghiep/{$business->tax_id}-" . Str::slug($business->name));
                    $sitemap->add(
                        Url::create($url)
                            ->setLastModificationDate(Carbon::now())
                            ->setChangeFrequency(Url::CHANGE_FREQUENCY_DAILY)
                            ->setPriority(0.8)
                    );
                }

                $filename = "sitemap-{$fileIndex}.xml";
                $sitemap->writeToFile(public_path($filename));

                $sitemapFiles[] = url($filename);
                $this->info("📝 Created: {$filename}");

                $fileIndex++;
            });

        // Tạo sitemap index
        $sitemapIndex = SitemapIndex::create();

        foreach ($sitemapFiles as $sitemapUrl) {
            $sitemapIndex->add(Url::create($sitemapUrl));
        }

        $sitemapIndex->writeToFile(public_path('sitemap-index.xml'));

        $this->info('✅ Sitemap index generated successfully at sitemap-index.xml');
    }
}
