<?php

namespace App\Livewire;

use App\Services\CheckPhatNguoiVNService;
use App\Services\CrawlCSGTService;
use Livewire\Component;

class TraCuuPhatNguoi extends Component
{
    public $bks;
    // public $result;
    public $empty = false;
    protected $checkPhatNguoiVNService;

    public function __construct()
    {
        $this->checkPhatNguoiVNService = new CheckPhatNguoiVNService();
    }

    public function search()
    {
        $this->bks = trim($this->bks);
        $this->reset('empty');

        $validated = $this->validate([
            'bks' => 'required|regex:/^\d{2}[a-zA-Z]{1,}\d{4,5}$/'
        ]);

        $this->bks = strtoupper($validated['bks']);

        $service = new CrawlCSGTService();

        $result = $service->phatnguoi($this->bks);
        if ($result && isset($result['status']) && $result['status']) {
            return $this->redirectRoute('detai.phat-nguoi', ['bks' => $result['vehicle_number']]);
        }
        if ($result && isset($result['status']) && $result['status'] === false) {
            if (isset($result['empty']) && $result['empty']) {
                $this->empty = true;
            } else {
                $this->addError('bks', 'Lỗi hệ thống, vui lòng thử lại.');
            }
        }

        // $this->result = $this->checkPhatNguoiVNService->phatNguoi($validated['bks']);
    }

    public function render()
    {
        return view('livewire.tra-cuu-phat-nguoi');
    }
}
