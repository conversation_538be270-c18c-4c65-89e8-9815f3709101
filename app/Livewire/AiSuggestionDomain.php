<?php

namespace App\Livewire;

use App\Services\ChatGPTService;
use Illuminate\Support\Facades\Cache;
use Livewire\Component;

class AiSuggestionDomain extends Component
{
    public $domains;
    public $business;
    protected $chatGPTService;
    public function __construct()
    {
        $this->chatGPTService = new ChatGPTService();
    }

    public function placeholder()
    {
        return view('livewire.placeholders.skeleton-loading-domain');
    }

    public function mount()
    {
        $cacheKey = "domain_suggestions_business_{$this->business->id}";
        if (Cache::has($cacheKey)) {
            $this->domains = Cache::get($cacheKey);
        } else {
            $prompt = "Bạn là chuyên gia gợi ý tên miền. 
            Với từ khóa '{$this->business->name}',
            hãy đề xuất 10 tên miền sáng tạo, độc đáo, chưa phổ biến, sử dụng các đuôi TLD sau: .vn, .com.vn, .com, .net, .top, .xyz, .ai (ưu tiên các TLD này).
            Mỗi tên miền phải khác biệt với các kết quả tìm kiếm cơ bản (không dùng trực tiếp '{$this->business->name}' làm tên miền chính mà có thể thêm tiền tố, hậu tố, hoặc biến thể sáng tạo.
            Mô tả ngắn bằng tiếng Việt cho từng tên miền. Trả về dưới dạng mảng JSON domain, description hợp lệ. Đảm bảo đầu ra là mảng JSON hợp lệ, không có văn bản thừa, bình luận hay định dạng markdown, không có định dạng ```json";
            $result = $this->chatGPTService->suggestionDomain($prompt);
            if ($result) {
                $this->domains = json_decode($result, true);
                Cache::add($cacheKey, json_decode($result, true), now()->addDay());
            }
        }
    }

    public function render()
    {
        return view('livewire.ai-suggestion-domain');
    }
}
