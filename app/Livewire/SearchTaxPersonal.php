<?php

namespace App\Livewire;

use App\Models\TaxPersonal;
use App\Services\CrawlTaxPersonalFromTCTService;
use Livewire\Component;
use Illuminate\Support\Str;

class SearchTaxPersonal extends Component
{
    public $s;
    public $result;
    protected $crawlTaxPersonalFromTCTService;
    public function __construct()
    {
        $this->crawlTaxPersonalFromTCTService = new CrawlTaxPersonalFromTCTService();
    }

    public function search()
    {
        $this->result = null;
        $this->s = trim($this->s);
        $validated = $this->validate([
            's' => ['required']
        ]);

        $item = TaxPersonal::where('tax_id', $validated['s'])
            ->orWhere('id_number', $validated['s'])
            ->first();

        if ($item) {
            return $this->redirectRoute('detail.personal', ['taxSlug' => $item->tax_id . '-' . Str::slug($item->name)]);
        } else {
            $info = $this->crawlTaxPersonalFromTCTService->handle($validated['s']);
            if ($info && isset($info['taxid'])) {
                $dataTax = [
                    'tax_id' => $info['taxid'],
                    'name' => $info['name'],
                    'id_number' => $info['id_number'],
                    'address' => $info['district'] . ' - ' . $info['province'],
                    'phone' => $info['phone'],
                    'issue_date' => !empty($info['issue_date']) ? now()->parse($info['issue_date'])->format('Y-m-d') : null,
                    'close_date' => $info['close_date'] ? now()->parse($info['close_date'])->format('Y-m-d') : null,
                    'tax_management_office' => $info['tax_management_office'],
                    'head_office_address' => $info['head_office_address'],
                    'status' => $info['note'],
                ];
                $newTax = TaxPersonal::create($dataTax);
                return $this->redirectRoute('detail.personal', ['taxSlug' => $newTax->tax_id . '-' . Str::slug($newTax->name)]);
            }
            return $this->addError('s', 'NNT Không trùng khớp với tìm kiếm của bạn.');
        }
    }

    public function render()
    {
        return view('livewire.search-tax-personal');
    }
}
