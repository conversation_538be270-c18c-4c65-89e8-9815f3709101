<?php

namespace App\Livewire;

use App\Models\TaxBusiness;
use App\Models\TaxPersonal;
use App\Models\Ward;
use App\Services\CrawlCSGTService;
use App\Services\CrawlTaxInfoFromTCTService;
use App\Services\CrawlTaxPersonalFromTCTService;
use Elastic\Elasticsearch\Client;
use Livewire\Component;
use Illuminate\Support\Str;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MatchPhraseQuery;

class QuickSearch extends Component
{
    public $position = 'header';
    public $type = 'business';
    public $searchText;
    public $placeholder = 'Nhập mã số thuế, tên công ty';
    public $emptyViolation = false;

    protected $crawlTaxPersonalFromTCTService;
    protected $crawlTaxInfoFromTCTService;
    public function __construct()
    {
        $this->crawlTaxPersonalFromTCTService = new CrawlTaxPersonalFromTCTService();
        $this->crawlTaxInfoFromTCTService = new CrawlTaxInfoFromTCTService();
    }

    public function updatedType()
    {
        $this->placeholder = match ($this->type) {
            'business' => 'Nhập mã số thuế, tên công ty',
            'personal' => 'Nhập CCCD, mã số thuế',
            'violation' => 'Nhập biển số xe, VD: 30A12345',
        };
        $this->resetErrorBag();
        $this->reset('searchText');
        $this->reset('emptyViolation');
    }

    public function search()
    {
        $this->searchText = trim($this->searchText);
        $rules = [
            'type' => ['required', 'in:business,personal,violation'],
            'searchText' => ['required'],
        ];
        if ($this->type == 'violation') {
            $rules['searchText'] = ['required', 'regex:/^\d{2}[a-zA-Z]{1,}\d{4,5}$/'];
        }
        $validated = $this->validate($rules);

        if ($validated['type'] == 'business') {
            $items = TaxBusiness::search('', function (Client $client, $body) use ($validated) {
                $boolQuery = new BoolQuery();
                $boolQuery->add(new MatchPhraseQuery('name', $validated['searchText']), 'should');
                $boolQuery->add(new MatchPhraseQuery('tax_id', $validated['searchText']), 'should');
                $body->addQuery($boolQuery);
                return $client->search(['body' => $body->toArray()])->asArray();
            })->get();
            if (count($items) > 1) {
                return $this->redirectRoute('search', ['q' => $validated['searchText']]);
            }
            if (count($items)  == 1) {
                $item = $items->first();
                if (preg_match('/^\d{1,}-\d{1,}$/', $item->tax_id, $matches)) {
                    $firstTax = explode("-", $item->tax_id)[0];
                    if ($firstTax == $validated['searchText']) {
                        $info = $this->crawlTaxInfoFromTCTService->handle($validated['searchText']);
                        if ($info && isset($info['taxid'])) {
                            $dataTax = [
                                'tax_id' => $info['taxid'],
                                'name' => $info['official_name'],
                                'alternate_name' => $info['official_name'],
                                'short_alternate_name' => $info['trading_name'],
                                'address' => $info['head_office_address'],
                                'representative_name' => $info['legal_representative'],
                                'id_number' => $info['legal_representative_id'],
                                'phone' => $info['phone'],
                                'establish_date' => now()->parse(str_replace("/", "-", $info['start_date']))->format('Y-m-d'),
                                'management_agency' => $info['tax_management_office'],
                                'company_type' => null,
                                'status' => $info['note'],
                            ];

                            if ($info['industries'] && !empty($info['industries'])) {
                                $industries = '';
                                $primary_industries = null;
                                foreach ($info['industries'] as $industry) {
                                    $industries .= $industry['code'] . ',';
                                    if ($industry['primary'] == 'Y') {
                                        $primary_industries = $industry['code'];
                                    }
                                }
                                $dataTax['industries'] = $industries;
                                $dataTax['primary_industry'] = $primary_industries;
                            }


                            $addressSplit = explode(',', $dataTax['address']);
                            $provinceName = $addressSplit[count($addressSplit) - 1] ?? null;
                            $districtName = $addressSplit[count($addressSplit) - 2] ?? null;
                            $wardName = $addressSplit[count($addressSplit) - 3] ?? null;
                            if ($wardName) {
                                $formatWardName = preg_replace_callback('/\d+/', function ($matches) {
                                    return str_pad($matches[0], 2, '0', STR_PAD_LEFT);
                                }, $wardName);
                                $ward = Ward::where(function ($query) use ($formatWardName, $wardName) {
                                    $query->where('full_name', 'like', '%' . trim($wardName) . '%')
                                        ->orWhere('full_name', 'like', '%' . trim($formatWardName) . '%');
                                })
                                    ->whereHas('district', function ($query) use ($districtName, $provinceName) {
                                        $query->where('full_name', 'like', '%' . trim($districtName) . '%')
                                            ->whereHas('province', function ($query) use ($provinceName) {
                                                $query->where('full_name', 'like', '%' . trim(str_replace("TP", "", $provinceName)) . '%');
                                            });
                                    })->first();
                                if ($ward) {
                                    $dataTax['ward_code'] = $ward->code;
                                }
                            }
                            $newTax = TaxBusiness::create($dataTax);
                            return $this->redirectRoute('detail.business', ['taxSlug' => $newTax->tax_id . '-' . Str::slug($newTax->name)]);
                        }
                    }
                }
                return $this->redirectRoute('detail.business', ['taxSlug' => $item->tax_id . '-' . Str::slug($item->name)]);
            }

            $info = $this->crawlTaxInfoFromTCTService->handle($validated['searchText']);
            if ($info && isset($info['taxid'])) {
                $dataTax = [
                    'tax_id' => $info['taxid'],
                    'name' => $info['official_name'],
                    'alternate_name' => $info['official_name'],
                    'short_alternate_name' => $info['trading_name'],
                    'address' => $info['head_office_address'],
                    'representative_name' => $info['legal_representative'],
                    'id_number' => $info['legal_representative_id'],
                    'phone' => $info['phone'],
                    'establish_date' => now()->parse(str_replace("/", "-", $info['start_date']))->format('Y-m-d'),
                    'management_agency' => $info['tax_management_office'],
                    'company_type' => null,
                    'status' => $info['note'],
                ];

                if ($info['industries'] && !empty($info['industries'])) {
                    $industries = '';
                    $primary_industries = null;
                    foreach ($info['industries'] as $industry) {
                        $industries .= $industry['code'] . ',';
                        if ($industry['primary'] == 'Y') {
                            $primary_industries = $industry['code'];
                        }
                    }
                    $dataTax['industries'] = $industries;
                    $dataTax['primary_industry'] = $primary_industries;
                }


                $addressSplit = explode(',', $dataTax['address']);
                $provinceName = $addressSplit[count($addressSplit) - 1] ?? null;
                $districtName = $addressSplit[count($addressSplit) - 2] ?? null;
                $wardName = $addressSplit[count($addressSplit) - 3] ?? null;
                if ($wardName) {
                    $formatWardName = preg_replace_callback('/\d+/', function ($matches) {
                        return str_pad($matches[0], 2, '0', STR_PAD_LEFT);
                    }, $wardName);
                    $ward = Ward::where(function ($query) use ($formatWardName, $wardName) {
                        $query->where('full_name', 'like', '%' . trim($wardName) . '%')
                            ->orWhere('full_name', 'like', '%' . trim($formatWardName) . '%');
                    })
                        ->whereHas('district', function ($query) use ($districtName, $provinceName) {
                            $query->where('full_name', 'like', '%' . trim($districtName) . '%')
                                ->whereHas('province', function ($query) use ($provinceName) {
                                    $query->where('full_name', 'like', '%' . trim(str_replace("TP", "", $provinceName)) . '%');
                                });
                        })->first();
                    if ($ward) {
                        $dataTax['ward_code'] = $ward->code;
                    }
                }
                $newTax = TaxBusiness::create($dataTax);
                return $this->redirectRoute('detail.business', ['taxSlug' => $newTax->tax_id . '-' . Str::slug($newTax->name)]);
            }
            return $this->addError('searchText', 'NNT Không trùng khớp với tìm kiếm của bạn.');
        }
        if ($validated['type'] == 'personal') {
            $item = TaxPersonal::where('tax_id', $validated['searchText'])
                ->orWhere('id_number', $validated['searchText'])
                ->first();

            if ($item) {
                return $this->redirectRoute('detail.personal', ['taxSlug' => $item->tax_id . '-' . Str::slug($item->name)]);
            } else {
                $info = $this->crawlTaxPersonalFromTCTService->handle($validated['searchText']);
                if ($info && isset($info['taxid'])) {
                    $dataTax = [
                        'tax_id' => $info['taxid'],
                        'name' => $info['name'],
                        'id_number' => $info['id_number'],
                        'address' => $info['district'] . ' - ' . $info['province'],
                        'phone' => $info['phone'],
                        'issue_date' => !empty($info['issue_date']) ? now()->parse($info['issue_date'])->format('Y-m-d') : null,
                        'close_date' => $info['close_date'] ? now()->parse($info['close_date'])->format('Y-m-d') : null,
                        'tax_management_office' => $info['tax_management_office'],
                        'head_office_address' => $info['head_office_address'],
                        'status' => $info['note'],
                    ];
                    $newTax = TaxPersonal::create($dataTax);
                    return $this->redirectRoute('detail.personal', ['taxSlug' => $newTax->tax_id . '-' . Str::slug($newTax->name)]);
                }
                return $this->addError('searchText', 'NNT Không trùng khớp với tìm kiếm của bạn.');
            }
        }
        if ($validated['type'] == 'violation') {
            $this->searchText = strtoupper($validated['searchText']);
            $service = new CrawlCSGTService();
            $result = $service->phatnguoi($validated['searchText']);
            if ($result && isset($result['status']) && $result['status']) {
                return $this->redirectRoute('detai.phat-nguoi', ['bks' => $result['vehicle_number']]);
            }
            if ($result && isset($result['status']) && $result['status'] === false) {
                if (isset($result['empty']) && $result['empty']) {
                    $this->emptyViolation = true;
                } else {
                    $this->addError('searchText', 'Lỗi hệ thống, vui lòng thử lại.');
                }
            }
        }
    }

    public function render()
    {
        return view('livewire.quick-search');
    }
}
