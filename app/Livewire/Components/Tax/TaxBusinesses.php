<?php

namespace App\Livewire\Components\Tax;

use App\Models\TaxBusiness;
use Elastic\Elasticsearch\Client;
use Livewire\Component;
use Livewire\WithPagination;

class TaxBusinesses extends Component
{
    use WithPagination;

    public function placeholder()
    {
        return view('livewire.placeholders.skeleton-tax-business');
    }

    public function render()
    {
        $tax_businesses = TaxBusiness::search()->take(20)->orderByDesc('updated_at')->get();

        return view('livewire.components.tax.tax-businesses', [
            'tax_businesses' => $tax_businesses,
        ]);
    }
}
