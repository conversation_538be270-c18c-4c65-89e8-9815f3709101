<?php

namespace App\Livewire\Components\Tax;

use App\Models\Industry;
use Livewire\Component;
use Livewire\WithPagination;

class Industries extends Component
{
    use WithPagination;

    public function mount()
    {
    }

    public function render()
    {
        $industries = Industry::paginate(20);
        return view('livewire.components.tax.industries', [
            'industries' => $industries
        ]);
    }
}
