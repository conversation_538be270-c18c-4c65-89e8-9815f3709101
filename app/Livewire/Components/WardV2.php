<?php

namespace App\Livewire\Components;

use App\Models\WardV2 as ModelsWardV2;
use Livewire\Component;

class WardV2 extends Component
{
    public $province_code;
    public $wards;
    public $business;
    public function mount()
    {
        $this->wards = ModelsWardV2::where('province_code', $this->province_code)->orderBy('name', 'asc')->get();
    }

    public function render()
    {
        return view('livewire.components.wardv2');
    }
}
