<?php

namespace App\Livewire\Components;

use App\Models\AddressConversion;
use App\Models\District;
use App\Models\OldDistrict;
use App\Models\OldProvince;
use App\Models\OldWard;
use App\Models\Province;
use App\Models\ProvinceV2;
use App\Models\Ward;
use App\Models\WardMapping;
use App\Models\WardV2;
use Flux\Flux;
use Livewire\Component;

class AdministrativeUnitsDemo extends Component
{
    public $selectedProvince = '';
    public $selectedWard = '';
    public $provinces = [];
    public $wardsCount = [];
    public $wards = [];
    public $selectedProvinceInfo = null;
    public $selectedWardInfo = null;
    public $old_provinces = [];
    public $old_districts = [];
    public $old_wards = [];
    public $selectedOldProvince;
    public $selectedOldDistrict;
    public $selectedOldWard;
    public $newAddress;

    public function mount()
    {
        $this->loadProvinces();
    }

    public function loadProvinces()
    {
        $this->provinces = ProvinceV2::orderBy('name', 'asc')->get();
        $this->old_provinces = OldProvince::orderBy('full_name', 'asc')->get();
        $this->wardsCount = WardV2::count();
    }

    public function updatedSelectedProvince($value)
    {
        $this->selectedWard = '';
        $this->selectedWardInfo = null;
        $this->selectedProvinceInfo = null;

        if ($value) {
            $this->selectedProvinceInfo = ProvinceV2::where('province_code', $value)->with('wards')->first();
            $this->loadWards($value);
        } else {
            $this->wards = [];
        }
    }

    public function updatedSelectedWard($value)
    {
        $this->selectedWardInfo = null;

        if ($value) {
            $this->selectedWardInfo = WardV2::where('ward_code', $value)->first();
        }
    }

    public function loadWards($provinceCode)
    {
        $this->wards = WardV2::where('province_code', $provinceCode)->orderBy('name', 'asc')->get();
    }

    public function resetForm()
    {
        $this->selectedProvince = '';
        $this->selectedWard = '';
        $this->wards = [];
        $this->selectedProvinceInfo = null;
        $this->selectedWardInfo = null;
    }

    public function updatedSelectedOldProvince($value)
    {
        $this->selectedOldDistrict = '';
        $this->selectedOldWard = '';
        $this->old_districts = [];
        $this->old_wards = [];

        if ($value) {
            $this->old_districts = OldDistrict::where('province_code', $value)->orderBy('full_name', 'asc')->get();
        }
    }

    public function updatedSelectedOldDistrict($value)
    {
        $this->selectedOldWard = '';
        $this->old_wards = [];

        if ($value) {
            $this->old_wards = OldWard::where('district_code', $value)->orderBy('full_name', 'asc')->get();
        }
    }

    public function convert()
    {
        $validated = $this->validate([
            'selectedOldProvince' => 'required',
            'selectedOldDistrict' => 'required',
            'selectedOldWard' => 'required',
        ]);
        // $oldProvince = OldProvince::where('code', $validated['selectedOldProvince'])->first();
        // $oldDistrict = OldDistrict::where('code', $validated['selectedOldDistrict'])->first();
        // $oldWard = OldWard::where('code', $validated['selectedOldWard'])->first();
        // $newAddress = AddressConversion::where('province_name', 'like', '%' . $oldProvince->full_name . '%')
        //     ->where('district_name', 'like', '%' . $oldDistrict->full_name . '%')
        //     ->where('ward_name', 'like', '%' . $oldWard->full_name . '%')
        //     ->get();
        $newAddress = WardMapping::where('old_ward_code', $validated['selectedOldWard'])->get();
        if (count($newAddress)) {
            $this->newAddress = $newAddress;
            Flux::toast('Đã chuyển đổi thành công', variant: 'success');
        } else {
            $this->reset('newAddress');
            Flux::toast('Không tìm thấy phường/xã mới', variant: 'danger');
        }
    }

    public function render()
    {
        return view('livewire.components.administrative-units-demo');
    }
}
