<?php

namespace App\Livewire\Components;

use App\Models\District as ModelsDistrict;
use App\Models\Province;
use Livewire\Component;

class District extends Component
{
    public $province_code;
    public $province;
    public $districts;
    public function mount()
    {
        $this->province = Province::where('code', $this->province_code)->first();
        $this->districts = ModelsDistrict::where('province_code', $this->province_code)->orderBy('full_name', 'asc')->get();
    }

    public function render()
    {
        return view('livewire.components.district');
    }
}
