<?php

namespace App\Livewire\Components;

use App\Models\AddressConversion;
use App\Services\AddressConversionService;
use Livewire\Component;
use Livewire\WithPagination;

class AddressConversionTool extends Component
{
    use WithPagination;

    public $searchTerm = '';
    public $searchType = 'all';
    public $searchResults = [];
    public $isSearching = false;
    public $showResults = false;
    public $statistics = null;
    public $creationResults = null;

    public $searchTypes = [
        'all' => 'Tất cả',
        'province' => 'Tỉnh/Thành phố',
        'district' => 'Quận/Huyện',
        'ward' => 'Phường/Xã'
    ];

    protected $rules = [
        'searchTerm' => 'required|string|min:2',
        'searchType' => 'required|in:all,province,district,ward'
    ];

    public function mount()
    {
        $this->loadStatistics();
    }

    public function loadStatistics()
    {
        try {
            $service = new AddressConversionService();
            $this->statistics = $service->getStatistics();
        } catch (\Exception $e) {
            session()->flash('error', 'Không thể tải thống kê: ' . $e->getMessage());
        }
    }

    public function search()
    {
        $this->validate();
        
        $this->isSearching = true;
        $this->searchResults = [];
        $this->showResults = false;
        $this->creationResults = null;

        try {
            $service = new AddressConversionService();
            $this->searchResults = $service->searchInDvhc($this->searchTerm, $this->searchType);
            $this->showResults = true;
            
            if (empty($this->searchResults)) {
                session()->flash('warning', 'Không tìm thấy kết quả nào cho từ khóa: ' . $this->searchTerm);
            } else {
                session()->flash('success', 'Tìm thấy ' . count($this->searchResults) . ' kết quả');
            }
            
        } catch (\Exception $e) {
            session()->flash('error', 'Lỗi tìm kiếm: ' . $e->getMessage());
        } finally {
            $this->isSearching = false;
        }
    }

    public function createAddressConversions()
    {
        if (empty($this->searchResults)) {
            session()->flash('error', 'Không có kết quả tìm kiếm để tạo bản ghi');
            return;
        }

        try {
            $service = new AddressConversionService();
            $this->creationResults = $service->createAddressConversions($this->searchResults, $this->searchTerm);
            
            $message = 'Đã tạo ' . $this->creationResults['total_created'] . ' bản ghi';
            if ($this->creationResults['total_errors'] > 0) {
                $message .= ', có ' . $this->creationResults['total_errors'] . ' lỗi';
            }
            
            session()->flash('success', $message);
            
        } catch (\Exception $e) {
            session()->flash('error', 'Lỗi tạo bản ghi: ' . $e->getMessage());
        }
    }

    public function clearResults()
    {
        $this->searchResults = [];
        $this->showResults = false;
        $this->creationResults = null;
        $this->searchTerm = '';
    }

    public function render()
    {
        $recentConversions = AddressConversion::latest()
            ->take(10)
            ->get();

        return view('livewire.components.address-conversion-tool', [
            'recentConversions' => $recentConversions
        ]);
    }
}
