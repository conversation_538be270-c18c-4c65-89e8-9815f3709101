<?php

namespace App\Livewire\Components;

use App\Jobs\UpdateTaxInfoFromTCTJob;
use App\Models\TaxBusiness;
use App\Services\CrawlMSTDN;
use Flux\Flux;
use Livewire\Component;

class DetailBusiness extends Component
{
    public $business;
    protected $crawlMSTDN;
    public function __construct()
    {
        $this->crawlMSTDN = new CrawlMSTDN();
    }

    public function reUpdate($tax_id)
    {
        UpdateTaxInfoFromTCTJob::dispatch($tax_id);
        Flux::toast('Thông tin doanh nghiệp sẽ được cập nhật.', variant: 'success');
        return;
    }

    public function render()
    {
        return view('livewire.components.detail-business');
    }
}
