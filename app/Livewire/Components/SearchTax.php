<?php

namespace App\Livewire\Components;

use App\Models\TaxBusiness;
use App\Models\TaxIndustry;
use App\Models\Ward;
use App\Models\WardV2;
use App\Services\CrawlMSTDN;
use App\Services\CrawlTaxInfoFromTCTService;
use DOMDocument;
use DOMXPath;
use Elastic\Elasticsearch\Client;
use Livewire\Component;
use Illuminate\Support\Str;
use Livewire\Attributes\Url;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MatchPhraseQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MatchQuery;
use ONGR\ElasticsearchDSL\Query\MatchAllQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermQuery;

class SearchTax extends Component
{
    #[Url]
    public $q;

    public $searchText;
    public $result;
    protected $crawlMSTDN;
    protected $crawlTaxInfoFromTCTService;
    public function __construct()
    {
        $this->crawlMSTDN = new CrawlMSTDN();
        $this->crawlTaxInfoFromTCTService = new CrawlTaxInfoFromTCTService();
    }

    public function search()
    {
        $this->result = null;
        $this->searchText = trim($this->searchText);
        $validated = $this->validate([
            'searchText' => ['required', 'min:4']
        ]);

        $items = TaxBusiness::search('', function (Client $client, $body) use ($validated) {
            $boolQuery = new BoolQuery();
            $boolQuery->add(new MatchPhraseQuery('name', $validated['searchText']), 'should');
            $boolQuery->add(new MatchPhraseQuery('tax_id', $validated['searchText']), 'should');
            $body->addQuery($boolQuery);
            return $client->search(['body' => $body->toArray()])->asArray();
        })->get();
        if (count($items) > 1) {
            return $this->redirectRoute('search', ['q' => $validated['searchText']]);
        }
        if (count($items)  == 1) {
            $item = $items->first();
            if (preg_match('/^\d{1,}-\d{1,}$/', $item->tax_id, $matches)) {
                $firstTax = explode("-", $item->tax_id)[0];
                if ($firstTax == $validated['searchText']) {
                    $info = $this->crawlTaxInfoFromTCTService->handle($validated['searchText']);
                    if ($info && isset($info['taxid'])) {
                        $dataTax = [
                            'tax_id' => $info['taxid'],
                            'name' => $info['official_name'],
                            'alternate_name' => $info['official_name'],
                            'short_alternate_name' => $info['trading_name'],
                            'address' => $info['head_office_address'],
                            'representative_name' => $info['legal_representative'],
                            'id_number' => $info['legal_representative_id'],
                            'phone' => $info['phone'],
                            'establish_date' => now()->parse(str_replace("/", "-", $info['start_date']))->format('Y-m-d'),
                            'management_agency' => $info['tax_management_office'],
                            'company_type' => null,
                            'status' => $info['note'],
                            'director_name' => $info['director_name'] ?? null,
                            'chief_accountant' => $info['chief_accountant'] ?? null,
                        ];

                        if ($info['industries'] && !empty($info['industries'])) {
                            $industries = '';
                            $primary_industries = null;
                            foreach ($info['industries'] as $industry) {
                                $industries .= $industry['code'] . ',';
                                if ($industry['primary'] == 'Y') {
                                    $primary_industries = $industry['code'];
                                }
                            }
                            $dataTax['industries'] = $industries;
                            $dataTax['primary_industry'] = $primary_industries;
                        }


                        $addressSplit = explode(',', $dataTax['address']);
                        $provinceName = $addressSplit[count($addressSplit) - 1] ?? null;
                        $wardName = $addressSplit[count($addressSplit) - 2] ?? null;
                        if ($wardName) {
                            $formatWardName = preg_replace_callback('/\d+/', function ($matches) {
                                return str_pad($matches[0], 2, '0', STR_PAD_LEFT);
                            }, $wardName);
                            $ward = WardV2::where(function ($query) use ($formatWardName, $wardName) {
                                $query->where('name', 'like', '%' . trim($wardName) . '%')
                                    ->orWhere('name', 'like', '%' . trim($formatWardName) . '%');
                            })
                                ->whereHas('province', function ($query) use ($provinceName) {
                                    $query->where('name', 'like', '%' . trim(str_replace("TP", "", $provinceName)) . '%');
                                })->first();
                            if ($ward) {
                                $dataTax['ward_code'] = $ward->ward_code;
                            }
                        }
                        $newTax = TaxBusiness::create($dataTax);
                        return $this->redirectRoute('detail.business', ['taxSlug' => $newTax->tax_id . '-' . Str::slug($newTax->name)]);
                    }
                }
            }
            return $this->redirectRoute('detail.business', ['taxSlug' => $item->tax_id . '-' . Str::slug($item->name)]);
        }

        $info = $this->crawlTaxInfoFromTCTService->handle($validated['searchText']);
        if ($info && isset($info['taxid'])) {
            $dataTax = [
                'tax_id' => $info['taxid'],
                'name' => $info['official_name'],
                'alternate_name' => $info['official_name'],
                'short_alternate_name' => $info['trading_name'],
                'address' => $info['head_office_address'],
                'representative_name' => $info['legal_representative'],
                'id_number' => $info['legal_representative_id'],
                'phone' => $info['phone'],
                'establish_date' => now()->parse(str_replace("/", "-", $info['start_date']))->format('Y-m-d'),
                'management_agency' => $info['tax_management_office'],
                'company_type' => null,
                'status' => $info['note'],
                'director_name' => $info['director_name'] ?? null,
                'chief_accountant' => $info['chief_accountant'] ?? null,
            ];

            if ($info['industries'] && !empty($info['industries'])) {
                $industries = '';
                $primary_industries = null;
                foreach ($info['industries'] as $industry) {
                    $industries .= $industry['code'] . ',';
                    if ($industry['primary'] == 'Y') {
                        $primary_industries = $industry['code'];
                    }
                }
                $dataTax['industries'] = $industries;
                $dataTax['primary_industry'] = $primary_industries;
            }


            $addressSplit = explode(',', $dataTax['address']);
            $provinceName = $addressSplit[count($addressSplit) - 1] ?? null;
            $wardName = $addressSplit[count($addressSplit) - 2] ?? null;
            if ($wardName) {
                $formatWardName = preg_replace_callback('/\d+/', function ($matches) {
                    return str_pad($matches[0], 2, '0', STR_PAD_LEFT);
                }, $wardName);
                $ward = WardV2::where(function ($query) use ($formatWardName, $wardName) {
                    $query->where('name', 'like', '%' . trim($wardName) . '%')
                        ->orWhere('name', 'like', '%' . trim($formatWardName) . '%');
                })
                    ->whereHas('province', function ($query) use ($provinceName) {
                        $query->where('name', 'like', '%' . trim(str_replace("TP", "", $provinceName)) . '%');
                    })->first();
                if ($ward) {
                    $dataTax['ward_code'] = $ward->ward_code;
                }
            }
            $newTax = TaxBusiness::where('tax_id', $dataTax['tax_id'])->first();
            if (!$newTax) {
                $newTax = TaxBusiness::create($dataTax);
            }
            return $this->redirectRoute('detail.business', ['taxSlug' => $newTax->tax_id . '-' . Str::slug($newTax->name)]);
            //                if ($info['industries'] && !empty($info['industries'])) {
            //                    $newIndustries = [];
            //                    foreach ($info['industries'] as $industry) {
            //                        $newIndustries[] = [
            //                            'tax_business_id' => $newTax->id,
            //                            'industry_code' => $industry['code'],
            //                            'primary' => $industry['primary'],
            //                        ];
            //                    }
            //                    TaxIndustry::insert($newIndustries);
            //                }
            // return $this->result = $newTax;
        }
        return $this->addError('searchText', 'NNT Không trùng khớp với tìm kiếm của bạn.');
    }

    public function mount()
    {
        $this->searchText = $this->q ?? '';
    }

    public function render()
    {
        return view('livewire.components.search-tax');
    }
}
