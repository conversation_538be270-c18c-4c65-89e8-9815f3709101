<?php

namespace App\Livewire\Components;

use App\Models\District;
use App\Models\Ward as ModelsWard;
use Livewire\Component;

class Ward extends Component
{
    public $district_code;
    public $district;
    public $wards;
    public function mount()
    {
        $this->district = District::where('code', $this->district_code)->first();
        $this->wards = ModelsWard::where('district_code', $this->district_code)->orderBy('full_name', 'asc')->get();
    }

    public function render()
    {
        return view('livewire.components.ward');
    }
}
