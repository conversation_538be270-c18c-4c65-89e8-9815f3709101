<?php

namespace App\Livewire;

use App\Models\Subscription as ModelsSubscription;
use Flux\Flux;
use Livewire\Component;

class Subscription extends Component
{
    public $email;

    public function subscription()
    {
        try {
            $validated = $this->validate([
                'email' => 'required|email|unique:subscriptions,email'
            ]);
        } catch (\Exception $e) {
            Flux::toast($e->getMessage(), variant: 'danger');
            return;
        }

        ModelsSubscription::create($validated);
        $this->reset('email');
        Flux::toast('Đăng ký nhận thông tin thành công', variant: 'success');
    }

    public function render()
    {
        return view('livewire.subscription');
    }
}
