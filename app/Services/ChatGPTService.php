<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Log;
use OpenAI;

class ChatGPTService
{
    protected $apiKey;
    protected $client;

    public function __construct()
    {
        $this->client = OpenAI::client(config('openai.api_key'));
    }

    public function suggestionDomain($prompt)
    {
        try {
            $result = $this->client->chat()->create([
                'model' => 'gpt-4-turbo',
                'messages' => [
                    ['role' => 'user', 'content' => $prompt],
                ],
            ]);
            return $result->choices[0]->message->content;
        } catch (Exception $e) {
            Log::error($e);
            return false;
        }
    }
}
