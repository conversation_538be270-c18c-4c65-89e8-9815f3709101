<?php

namespace App\Services;

use GuzzleHttp\Client;

class CheckPhatNguoiVNService
{
    protected $client;

    public function __construct()
    {
        $this->client = new Client(['base_uri' => 'https://api.checkphatnguoi.vn']);
    }

    public function phatNguoi($bks)
    {
        $result = $this->client->post('/phatnguoi', [
            'json' => [
                'bienso' => strtoupper($bks)
            ]
        ]);
        return json_decode($result->getBody()->getContents(), true);
    }
}
