<?php

namespace App\Services;

use DOMDocument;
use DOMXPath;
use GuzzleHttp\Client;
use GuzzleHttp\Cookie\CookieJar;
use Illuminate\Support\Facades\Log;

class BoCaoDienTuService
{
    public function newRegister()
    {
        $cookie = new CookieJar();
        $client = new Client();
        $currentPage = 1;
        $totalPages = 1;
        $formData = [
            '__EVENTTARGET' => 'ctl00$C$RptProdGroups$ctl01$LnkActiveAnnType',
            '__EVENTARGUMENT' => '',
            '__VIEWSTATE' => '',
        ];
        $companies = [];
        do {
            $response = $client->request('POST', 'https://bocaodientu.dkkd.gov.vn/egazette/Forms/Egazette/DefaultAnnouncements.aspx', [
                'cookies' => $cookie,
                'verify' => false,
                'form_params' => $formData,
            ]);
            $htmlContent = $response->getBody()->getContents();
            $htmlContent = str_ireplace('<head>', '<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8" />', $htmlContent);

            $dom = new DOMDocument();
            libxml_use_internal_errors(true);
            @$dom->loadHTML($htmlContent);
            libxml_clear_errors();

            $xpath = new DOMXPath($dom);
            //get tax id
            $rows = $xpath->query('//table[@class="gridview"]/tr[position() > 1 and position() < last()]');

            foreach ($rows as $row) {
                $codeNode = $xpath->query('.//td[2]//div[@class="enterprise_code"]/span', $row)->item(0);
                $code = trim(str_replace('MÃ SỐ DN:', '', $codeNode->nodeValue));
                // Lưu vào danh sách
                $companies[] = [
                    'code' => preg_replace('/\D+/', '', $code),
                ];
            }


            //get hidden input
            $hiddenInputs = $xpath->query('//input[@type="hidden"]');

            $hiddenData = [];
            foreach ($hiddenInputs as $input) {
                $name = $input->getAttribute('name');
                $value = $input->getAttribute('value');
                $hiddenData[$name] = $value;
            }


            $pageLinks = $xpath->query('//tr[@class="Pager"]//a');

            $pages = [];
            foreach ($pageLinks as $link) {
                $pages[] = trim($link->nodeValue);
            }

            // Lấy số trang lớn nhất (page cuối cùng)
            $totalPages = max($pages);
            $currentPage++;
            $formData = [
                ...$hiddenData,
                '__EVENTTARGET' => 'ctl00$C$CtlList',
                '__EVENTARGUMENT' => 'Page$' . $currentPage,
                '__VIEWSTATE' => '',
            ];
        } while ($currentPage <= $totalPages);


        return array_reverse($companies);
    }

    public function registerChange()
    {
        $cookie = new CookieJar();
        $client = new Client();

        $response = $client->request('POST', 'https://bocaodientu.dkkd.gov.vn/egazette/Forms/Egazette/DefaultAnnouncements.aspx', [
            'cookies' => $cookie,
            'verify' => false,
        ]);
        $htmlContent = $response->getBody()->getContents();
        $htmlContent = str_ireplace('<head>', '<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8" />', $htmlContent);

        $dom = new DOMDocument();
        libxml_use_internal_errors(true);
        @$dom->loadHTML($htmlContent);
        libxml_clear_errors();

        $xpath = new DOMXPath($dom);


        //get hidden input
        $hiddenInputs = $xpath->query('//input[@type="hidden"]');

        $hiddenData = [];
        foreach ($hiddenInputs as $input) {
            $name = $input->getAttribute('name');
            $value = $input->getAttribute('value');
            $hiddenData[$name] = $value;
        }

        $formData = [
            ...$hiddenData,
            '__EVENTTARGET' => 'ctl00$C$RptProdGroups$ctl02$LnkActiveAnnType',
            '__EVENTARGUMENT' => '',
            '__VIEWSTATE' => '',
        ];

        $currentPage = 1;
        $totalPages = 1;
        $companies = [];
        do {
            $response = $client->request('POST', 'https://bocaodientu.dkkd.gov.vn/egazette/Forms/Egazette/DefaultAnnouncements.aspx', [
                'cookies' => $cookie,
                'verify' => false,
                'form_params' => $formData,
            ]);
            $htmlContent = $response->getBody()->getContents();
            $htmlContent = str_ireplace('<head>', '<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8" />', $htmlContent);

            $dom = new DOMDocument();
            libxml_use_internal_errors(true);
            @$dom->loadHTML($htmlContent);
            libxml_clear_errors();

            $xpath = new DOMXPath($dom);
            //get tax id
            $rows = $xpath->query('//table[@class="gridview"]/tr[position() > 1 and position() < last()]');

            foreach ($rows as $row) {
                $codeNode = $xpath->query('.//td[2]//div[@class="enterprise_code"]/span', $row)->item(0);
                $code = trim(str_replace('MÃ SỐ DN:', '', $codeNode->nodeValue));
                // Lưu vào danh sách
                $companies[] = [
                    'code' => preg_replace('/\D+/', '', $code),
                ];
            }


            //get hidden input
            $hiddenInputs = $xpath->query('//input[@type="hidden"]');

            $hiddenData = [];
            foreach ($hiddenInputs as $input) {
                $name = $input->getAttribute('name');
                $value = $input->getAttribute('value');
                $hiddenData[$name] = $value;
            }


            $pageLinks = $xpath->query('//tr[@class="Pager"]//a');

            $pages = [];
            foreach ($pageLinks as $link) {
                $pages[] = trim($link->nodeValue);
            }

            // Lấy số trang lớn nhất (page cuối cùng)
            $totalPages = max($pages);
            $currentPage++;
            $formData = [
                ...$hiddenData,
                '__EVENTTARGET' => 'ctl00$C$CtlList',
                '__EVENTARGUMENT' => 'Page$' . $currentPage,
                '__VIEWSTATE' => '',
            ];
        } while ($currentPage <= $totalPages);


        return array_reverse($companies);
    }

    public function updateChanged()
    {
        $cookie = new CookieJar();
        $client = new Client();

        $response = $client->request('POST', 'https://bocaodientu.dkkd.gov.vn/egazette/Forms/Egazette/DefaultAnnouncements.aspx', [
            'cookies' => $cookie,
            'verify' => false,
        ]);
        $htmlContent = $response->getBody()->getContents();
        $htmlContent = str_ireplace('<head>', '<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8" />', $htmlContent);

        $dom = new DOMDocument();
        libxml_use_internal_errors(true);
        @$dom->loadHTML($htmlContent);
        libxml_clear_errors();

        $xpath = new DOMXPath($dom);


        //get hidden input
        $hiddenInputs = $xpath->query('//input[@type="hidden"]');

        $hiddenData = [];
        foreach ($hiddenInputs as $input) {
            $name = $input->getAttribute('name');
            $value = $input->getAttribute('value');
            $hiddenData[$name] = $value;
        }

        $formData = [
            ...$hiddenData,
            '__EVENTTARGET' => 'ctl00$C$RptProdGroups$ctl03$LnkActiveAnnType',
            '__EVENTARGUMENT' => '',
            '__VIEWSTATE' => '',
        ];

        $currentPage = 1;
        $totalPages = 1;
        $companies = [];
        do {
            $response = $client->request('POST', 'https://bocaodientu.dkkd.gov.vn/egazette/Forms/Egazette/DefaultAnnouncements.aspx', [
                'cookies' => $cookie,
                'verify' => false,
                'form_params' => $formData,
            ]);
            $htmlContent = $response->getBody()->getContents();
            $htmlContent = str_ireplace('<head>', '<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8" />', $htmlContent);

            $dom = new DOMDocument();
            libxml_use_internal_errors(true);
            @$dom->loadHTML($htmlContent);
            libxml_clear_errors();

            $xpath = new DOMXPath($dom);
            //get tax id
            $rows = $xpath->query('//table[@class="gridview"]/tr[position() > 1 and position() < last()]');

            foreach ($rows as $row) {
                $codeNode = $xpath->query('.//td[2]//div[@class="enterprise_code"]/span', $row)->item(0);
                $code = trim(str_replace('MÃ SỐ DN:', '', $codeNode->nodeValue));
                // Lưu vào danh sách
                $companies[] = [
                    'code' => preg_replace('/\D+/', '', $code),
                ];
            }


            //get hidden input
            $hiddenInputs = $xpath->query('//input[@type="hidden"]');

            $hiddenData = [];
            foreach ($hiddenInputs as $input) {
                $name = $input->getAttribute('name');
                $value = $input->getAttribute('value');
                $hiddenData[$name] = $value;
            }


            $pageLinks = $xpath->query('//tr[@class="Pager"]//a');

            $pages = [];
            foreach ($pageLinks as $link) {
                $pages[] = trim($link->nodeValue);
            }

            // Lấy số trang lớn nhất (page cuối cùng)
            $totalPages = max($pages);
            $currentPage++;
            $formData = [
                ...$hiddenData,
                '__EVENTTARGET' => 'ctl00$C$CtlList',
                '__EVENTARGUMENT' => 'Page$' . $currentPage,
                '__VIEWSTATE' => '',
            ];
        } while ($currentPage <= $totalPages);


        return array_reverse($companies);
    }
}
