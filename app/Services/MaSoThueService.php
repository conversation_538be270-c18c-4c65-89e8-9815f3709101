<?php

namespace App\Services;

use DOMDocument;
use DOMXPath;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use TwoCaptcha\TwoCaptcha;

class MaSoThueService
{
    protected $client;

    public function __construct()
    {
        $this->client = new Client(['base_uri' => 'https://masothue.com']);
    }

    public function call($endpoint, $method = 'GET', $page = 1)
    {
        try {
            $headers = [
                "user-agent" => "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            ];
            $host = config('proxy.host');
            $port = config('proxy.port');
            $username = config('proxy.username');
            $password = config('proxy.password');

            $encodedPassword = rawurlencode($password);
            $response = $this->client->request($method, $endpoint, [
                'headers' => $headers,
                // 'proxy' => "http://$username:$encodedPassword@$host:$port",
                'query' => [
                    'page' => $page
                ]
            ]);

            return $response->getBody()->getContents();
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            Log::error($e);
            Log::debug($method);
            Log::debug($endpoint);
            if ($e->getResponse()->getStatusCode() == 404) {
                exit;
            }
            if ($e->getResponse()->getStatusCode() == 403) {
                $htmlContent = $e->getResponse()->getBody()->getContents();
                $dom = new DOMDocument();
                @$dom->loadHTML($htmlContent);
                $xpath = new DOMXPath($dom);
                $nodes = $xpath->query("//*[contains(text(), 'permission to access this resource')]");

                if ($nodes->length > 0) {
                    Log::warning('Link 403, skip');
                    exit;
                }

                Log::warning('API returned 403, pausing jobs...');

                if (Cache::get('jobs_paused', false)) {
                    return false;
                }

                // Pause all jobs
                Cache::put('jobs_paused', true, now()->addMinutes(30)); // Pause for 30 mins

                $solver = new TwoCaptcha(config('twocaptcha.api_key'));
                $result = $solver->recaptcha([
                    'sitekey' => '6LcHY_kZAAAAALb2De4UUonssmRquIktWuABe-W5',
                    'url'     => 'https://masothue.com',
                ]);
                if ($result && $result->code) {
                    Log::warning('Start Bypass captcha: ' . $result->code);
                    $client = new Client();
                    $res = $client->request('POST', 'https://masothue.com/Ajax/checkHumanCaptcha', [
                        'headers' => [
                            'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                        ],
                        // 'proxy' => "http://$username:$encodedPassword@$host:$port",
                        'form_params' => [
                            'reCaptcha' => $result->code,
                        ]
                    ]);
                    Cache::forget('jobs_paused');
                    Log::warning('Done Bypass captcha -> jobs_paused -> false');
                    return false;
                }
                return false;

                // $api = new RecaptchaV2Proxyless();
                // $api->setVerboseMode(false);

                // //your anti-captcha.com account key
                // $api->setKey('1656786af4e039f663a987184a341f67');

                // //recaptcha key from target website
                // $api->setWebsiteURL("https://masothue.com");
                // $api->setWebsiteKey("6LcHY_kZAAAAALb2De4UUonssmRquIktWuABe-W5");

                // //Specify softId to earn 10% commission with your app.
                // //Get your softId here: https://anti-captcha.com/clients/tools/devcenter
                // $api->setSoftId(0);

                // if (!$api->createTask()) {
                //     $api->debout("API v2 send failed - " . $api->getErrorMessage(), "red");
                //     return false;
                // }

                // $taskId = $api->getTaskId();


                // if (!$api->waitForResult()) {
                //     $api->debout("could not solve captcha", "red");
                //     $api->debout($api->getErrorMessage());
                // } else {
                //     $recaptchaToken =   $api->getTaskSolution();
                //     Log::warning('Bypass captcha: ' . $recaptchaToken);
                //     $client = new Client();
                //     $client->request('POST', 'https://masothue.com/Ajax/checkHumanCaptcha', [
                //         'headers' => [
                //             'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                //         ],
                //         'form_params' => [
                //             'reCaptcha' => $recaptchaToken,
                //         ]
                //     ]);
                //     Cache::forget('jobs_paused');

                //     return false;
                // }
            }
        }
    }
}
