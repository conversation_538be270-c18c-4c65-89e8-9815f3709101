<?php

namespace App\Services;

use App\Models\Province;
use App\Models\Ward;

class AddressConversionService
{
    private $dvhcData;

    public function __construct()
    {
        $this->loadDvhcData();
    }

    /**
     * Load DVHC data from JSON file
     */
    private function loadDvhcData()
    {
        $jsonPath = database_path('data/dvhc.json');

        if (!file_exists($jsonPath)) {
            throw new \Exception('DVHC JSON file not found at: ' . $jsonPath);
        }

        $jsonContent = file_get_contents($jsonPath);
        $this->dvhcData = json_decode($jsonContent, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception('Invalid JSON in DVHC file: ' . json_last_error_msg());
        }
    }

    /**
     * Search for old ward names in DVHC data
     * DVHC Structure:
     * Column 0: New Province
     * Column 1: Merged from
     * Column 2: New Ward
     * Column 3: Old Wards (comma separated)
     *
     * @param string $searchTerm
     * @param string $type - 'province', 'district', or 'ward'
     * @return array
     */
    public function searchInDvhc($searchTerm, $type = 'all')
    {
        $results = [];
        $searchTerm = $this->normalizeSearchTerm($searchTerm);

        foreach ($this->dvhcData['rows'] as $row) {
            $columns = $row['columns'];
            $newProvinceName = $columns[0] ?? '';      // Column 0: New Province
            $mergedFromInfo = $columns[1] ?? '';       // Column 1: Merged from
            $newWardName = $columns[2] ?? '';          // Column 2: New Ward
            $oldWardNames = $columns[3] ?? '';         // Column 3: Old Wards

            // Search based on type
            switch ($type) {
                case 'province':
                    // Search in new province names
                    if ($this->matchesSearchTerm($newProvinceName, $searchTerm)) {
                        $results[] = [
                            'type' => 'province',
                            'new_province' => $newProvinceName,
                            'merged_from' => $mergedFromInfo,
                            'new_ward' => $newWardName,
                            'old_wards' => $oldWardNames,
                            'full_row' => $columns
                        ];
                    }
                    break;

                case 'ward':
                    // Search in old ward names (main use case)
                    if ($this->matchesSearchTerm($oldWardNames, $searchTerm)) {
                        $results[] = [
                            'type' => 'ward',
                            'new_province' => $newProvinceName,
                            'merged_from' => $mergedFromInfo,
                            'new_ward' => $newWardName,
                            'matching_old_ward' => $this->extractMatchingWard($oldWardNames, $searchTerm),
                            'all_old_wards' => $oldWardNames,
                            'full_row' => $columns
                        ];
                    }
                    break;

                default: // 'all'
                    $matches = [];
                    if ($this->matchesSearchTerm($newProvinceName, $searchTerm)) {
                        $matches[] = 'new_province';
                    }
                    if ($this->matchesSearchTerm($newWardName, $searchTerm)) {
                        $matches[] = 'new_ward';
                    }
                    if ($this->matchesSearchTerm($oldWardNames, $searchTerm)) {
                        $matches[] = 'old_ward';
                    }

                    if (!empty($matches)) {
                        $results[] = [
                            'types' => $matches,
                            'new_province' => $newProvinceName,
                            'merged_from' => $mergedFromInfo,
                            'new_ward' => $newWardName,
                            'old_wards' => $oldWardNames,
                            'search_term' => $searchTerm,
                            'full_row' => $columns
                        ];
                    }
                    break;
            }
        }

        return $results;
    }

    /**
     * Parse complex ward list that may contain descriptions in parentheses
     */
    private function parseComplexWardList($wardListString)
    {
        if (empty($wardListString)) {
            return [];
        }

        // Split by comma first
        $rawWards = explode(',', $wardListString);
        $cleanedWards = [];

        foreach ($rawWards as $ward) {
            $ward = trim($ward);

            // Handle wards with parenthetical descriptions
            // Example: "Phường Bạch Đằng (phần còn lại sau khi sáp nhập vào phường Hai Bà Trưng)"
            if (preg_match('/^(.+?)\s*\([^)]+\)$/', $ward, $matches)) {
                // Extract the ward name before the parentheses
                $cleanedWards[] = trim($matches[1]);
            } else {
                // Regular ward name
                $cleanedWards[] = $ward;
            }
        }

        return array_filter($cleanedWards); // Remove empty entries
    }

    /**
     * Normalize search term for better matching
     */
    private function normalizeSearchTerm($term)
    {
        $term = $this->convertToUtf8(trim($term));
        return mb_strtolower($term, 'UTF-8');
    }

    /**
     * Check if a text matches the search term
     */
    private function matchesSearchTerm($text, $searchTerm)
    {
        $normalizedText = $this->normalizeSearchTerm($text);
        $normalizedSearchTerm = $this->normalizeSearchTerm($searchTerm);
        return mb_strpos($normalizedText, $normalizedSearchTerm, 0, 'UTF-8') !== false;
    }

    /**
     * Extract the specific ward name that matches the search term
     */
    private function extractMatchingWard($wardNames, $searchTerm)
    {
        $wardNames = $this->convertToUtf8($wardNames);
        $wards = $this->parseComplexWardList($wardNames);

        foreach ($wards as $ward) {
            if ($this->matchesSearchTerm($ward, $searchTerm)) {
                return $ward;
            }
        }

        return $wardNames; // Return all if no specific match
    }

    /**
     * Find current province in database
     */
    private function findCurrentProvince($provinceName)
    {
        // Direct match
        $province = Province::where('full_name', $provinceName)->first();

        if (!$province) {
            // Fuzzy match
            $province = Province::where('full_name', 'like', '%' . $provinceName . '%')->first();
        }

        return $province;
    }

    /**
     * Find current ward in database
     */
    private function findCurrentWard($wardName)
    {
        // Direct match
        $ward = Ward::where('full_name', $wardName)->first();

        if (!$ward) {
            // Fuzzy match
            $ward = Ward::where('full_name', 'like', '%' . $wardName . '%')->first();
        }

        return $ward;
    }

    /**
     * Build new full address
     */
    private function buildNewFullAddress($ward, $province, $district = null)
    {
        $parts = [];

        if ($ward) {
            $parts[] = $ward->full_name;

            if ($ward->district) {
                $parts[] = $ward->district->full_name;
            }
        } elseif ($district) {
            $parts[] = $district->full_name;
        }

        if ($province) {
            $parts[] = $province->full_name;
        }

        return implode(', ', $parts);
    }

    /**
     * Convert string to UTF-8 encoding
     */
    public function convertToUtf8($text)
    {
        if (empty($text)) {
            return $text;
        }

        // Check if the string is already UTF-8
        if (mb_check_encoding($text, 'UTF-8')) {
            return $text;
        }

        // Try to detect encoding and convert to UTF-8
        $encoding = mb_detect_encoding($text, ['UTF-8', 'ISO-8859-1', 'Windows-1252', 'ASCII'], true);

        if ($encoding && $encoding !== 'UTF-8') {
            $converted = mb_convert_encoding($text, 'UTF-8', $encoding);
            return $converted !== false ? $converted : $text;
        }

        // If detection fails, try common encodings
        $commonEncodings = ['ISO-8859-1', 'Windows-1252', 'CP1252'];

        foreach ($commonEncodings as $fromEncoding) {
            $converted = @mb_convert_encoding($text, 'UTF-8', $fromEncoding);
            if ($converted !== false && mb_check_encoding($converted, 'UTF-8')) {
                return $converted;
            }
        }

        // If all else fails, remove non-UTF-8 characters
        return mb_convert_encoding($text, 'UTF-8', 'UTF-8');
    }

    /**
     * Get statistics about DVHC data
     */
    public function getStatistics()
    {
        $totalRows = count($this->dvhcData['rows']);
        $provinces = [];
        $newWards = [];
        $totalOldWards = 0;

        foreach ($this->dvhcData['rows'] as $row) {
            $columns = $row['columns'];
            $newProvinceName = $columns[0] ?? '';      // Column 0: New Province
            $newWardName = $columns[2] ?? '';          // Column 2: New Ward
            $oldWardNames = $columns[3] ?? '';         // Column 3: Old Wards

            if (!in_array($newProvinceName, $provinces)) {
                $provinces[] = $newProvinceName;
            }

            if (!in_array($newWardName, $newWards)) {
                $newWards[] = $newWardName;
            }

            $oldWardCount = count($this->parseComplexWardList($oldWardNames));
            $totalOldWards += $oldWardCount;
        }

        return [
            'total_rows' => $totalRows,
            'unique_new_provinces' => count($provinces),
            'unique_new_wards' => count($newWards),
            'total_old_wards' => $totalOldWards,
            'provinces_list' => $provinces
        ];
    }
}
