<?php

namespace App\Services;

use App\Models\AddressConversion;
use App\Models\Province;
use App\Models\District;
use App\Models\Ward;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AddressConversionService
{
    private $dvhcData;

    public function __construct()
    {
        $this->loadDvhcData();
    }

    /**
     * Load DVHC data from JSON file
     */
    private function loadDvhcData()
    {
        $jsonPath = database_path('data/dvhc.json');
        
        if (!file_exists($jsonPath)) {
            throw new \Exception('DVHC JSON file not found at: ' . $jsonPath);
        }

        $jsonContent = file_get_contents($jsonPath);
        $this->dvhcData = json_decode($jsonContent, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception('Invalid JSON in DVHC file: ' . json_last_error_msg());
        }
    }

    /**
     * Search for old administrative unit names in DVHC data
     * 
     * @param string $searchTerm
     * @param string $type - 'province', 'district', or 'ward'
     * @return array
     */
    public function searchInDvhc($searchTerm, $type = 'all')
    {
        $results = [];
        $searchTerm = $this->normalizeSearchTerm($searchTerm);

        foreach ($this->dvhcData['rows'] as $row) {
            $columns = $row['columns'];
            $provinceName = $columns[0] ?? '';
            $districtName = $columns[1] ?? '';
            $wardNames = $columns[2] ?? '';

            // Search in different columns based on type
            switch ($type) {
                case 'province':
                    if ($this->matchesSearchTerm($provinceName, $searchTerm)) {
                        $results[] = [
                            'type' => 'province',
                            'old_name' => $provinceName,
                            'district' => $districtName,
                            'wards' => $wardNames,
                            'full_row' => $columns
                        ];
                    }
                    break;
                
                case 'district':
                    if ($this->matchesSearchTerm($districtName, $searchTerm)) {
                        $results[] = [
                            'type' => 'district',
                            'province' => $provinceName,
                            'old_name' => $districtName,
                            'wards' => $wardNames,
                            'full_row' => $columns
                        ];
                    }
                    break;
                
                case 'ward':
                    if ($this->matchesSearchTerm($wardNames, $searchTerm)) {
                        $results[] = [
                            'type' => 'ward',
                            'province' => $provinceName,
                            'district' => $districtName,
                            'old_name' => $this->extractMatchingWard($wardNames, $searchTerm),
                            'all_wards' => $wardNames,
                            'full_row' => $columns
                        ];
                    }
                    break;
                
                default: // 'all'
                    $matches = [];
                    if ($this->matchesSearchTerm($provinceName, $searchTerm)) {
                        $matches[] = 'province';
                    }
                    if ($this->matchesSearchTerm($districtName, $searchTerm)) {
                        $matches[] = 'district';
                    }
                    if ($this->matchesSearchTerm($wardNames, $searchTerm)) {
                        $matches[] = 'ward';
                    }
                    
                    if (!empty($matches)) {
                        $results[] = [
                            'types' => $matches,
                            'province' => $provinceName,
                            'district' => $districtName,
                            'wards' => $wardNames,
                            'search_term' => $searchTerm,
                            'full_row' => $columns
                        ];
                    }
                    break;
            }
        }

        return $results;
    }

    /**
     * Create AddressConversion records from DVHC search results
     * 
     * @param array $searchResults
     * @param string $searchTerm
     * @return array
     */
    public function createAddressConversions($searchResults, $searchTerm)
    {
        $created = [];
        $errors = [];

        foreach ($searchResults as $result) {
            try {
                $oldProvinceName = $result['province'] ?? '';
                $oldDistrictName = $result['district'] ?? '';
                $oldWardNames = $result['wards'] ?? '';

                // Try to find matching new administrative units
                $newProvince = $this->findNewProvince($oldProvinceName);
                $newWard = $this->findNewWard($oldWardNames, $oldDistrictName, $oldProvinceName);

                // Parse individual ward names from the comma-separated string
                $wardList = array_map('trim', explode(',', $oldWardNames));
                
                foreach ($wardList as $wardName) {
                    if (empty($wardName)) continue;

                    $addressConversion = AddressConversion::create([
                        'ward_name' => $wardName,
                        'district_name' => $oldDistrictName,
                        'province_name' => $oldProvinceName,
                        'detail_address' => $searchTerm,
                        'new_detail' => $newWard ? $newWard->full_name : '',
                        'new_ward' => $newWard ? $newWard->full_name : '',
                        'new_ward_code' => $newWard ? $newWard->code : '',
                        'new_province' => $newProvince ? $newProvince->full_name : '',
                        'new_province_code' => $newProvince ? $newProvince->code : '',
                        'new_full_address' => $this->buildNewFullAddress($newWard, $newProvince),
                        'not_sure' => !$newWard || !$newProvince,
                        'success' => $newWard && $newProvince,
                        'ward_code_missing' => !$newWard,
                        'province_code_missing' => !$newProvince,
                    ]);

                    $created[] = $addressConversion;
                }

            } catch (\Exception $e) {
                $errors[] = [
                    'result' => $result,
                    'error' => $e->getMessage()
                ];
            }
        }

        return [
            'created' => $created,
            'errors' => $errors,
            'total_created' => count($created),
            'total_errors' => count($errors)
        ];
    }

    /**
     * Normalize search term for better matching
     */
    private function normalizeSearchTerm($term)
    {
        return Str::lower(trim($term));
    }

    /**
     * Check if a text matches the search term
     */
    private function matchesSearchTerm($text, $searchTerm)
    {
        $normalizedText = $this->normalizeSearchTerm($text);
        return Str::contains($normalizedText, $searchTerm);
    }

    /**
     * Extract the specific ward name that matches the search term
     */
    private function extractMatchingWard($wardNames, $searchTerm)
    {
        $wards = array_map('trim', explode(',', $wardNames));
        
        foreach ($wards as $ward) {
            if ($this->matchesSearchTerm($ward, $searchTerm)) {
                return $ward;
            }
        }
        
        return $wardNames; // Return all if no specific match
    }

    /**
     * Find new province based on old province name
     */
    private function findNewProvince($oldProvinceName)
    {
        // Try exact match first
        $province = Province::where('full_name', $oldProvinceName)->first();
        
        if (!$province) {
            // Try partial match
            $province = Province::where('full_name', 'like', '%' . $oldProvinceName . '%')->first();
        }
        
        return $province;
    }

    /**
     * Find new ward based on old ward information
     */
    private function findNewWard($oldWardNames, $oldDistrictName, $oldProvinceName)
    {
        $wardList = array_map('trim', explode(',', $oldWardNames));
        
        foreach ($wardList as $wardName) {
            // Try to find ward by name
            $ward = Ward::where('full_name', 'like', '%' . $wardName . '%')->first();
            
            if ($ward) {
                return $ward;
            }
        }
        
        return null;
    }

    /**
     * Build new full address string
     */
    private function buildNewFullAddress($newWard, $newProvince)
    {
        $parts = [];
        
        if ($newWard) {
            $parts[] = $newWard->full_name;
            
            if ($newWard->district) {
                $parts[] = $newWard->district->full_name;
            }
        }
        
        if ($newProvince) {
            $parts[] = $newProvince->full_name;
        }
        
        return implode(', ', $parts);
    }

    /**
     * Get statistics about DVHC data
     */
    public function getStatistics()
    {
        $totalRows = count($this->dvhcData['rows']);
        $provinces = [];
        $districts = [];
        $totalWards = 0;

        foreach ($this->dvhcData['rows'] as $row) {
            $columns = $row['columns'];
            $provinceName = $columns[0] ?? '';
            $districtName = $columns[1] ?? '';
            $wardNames = $columns[2] ?? '';

            if (!in_array($provinceName, $provinces)) {
                $provinces[] = $provinceName;
            }
            
            $districtKey = $provinceName . '|' . $districtName;
            if (!in_array($districtKey, $districts)) {
                $districts[] = $districtKey;
            }
            
            $wardCount = count(array_filter(array_map('trim', explode(',', $wardNames))));
            $totalWards += $wardCount;
        }

        return [
            'total_rows' => $totalRows,
            'unique_provinces' => count($provinces),
            'unique_districts' => count($districts),
            'estimated_total_wards' => $totalWards,
            'provinces_list' => $provinces
        ];
    }
}
