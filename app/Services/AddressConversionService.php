<?php

namespace App\Services;

use App\Models\AddressConversion;
use App\Models\Province;
use App\Models\District;
use App\Models\Ward;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AddressConversionService
{
    private $dvhcData;

    public function __construct()
    {
        $this->loadDvhcData();
    }

    /**
     * Load DVHC data from JSON file
     */
    private function loadDvhcData()
    {
        $jsonPath = database_path('data/dvhc.json');

        if (!file_exists($jsonPath)) {
            throw new \Exception('DVHC JSON file not found at: ' . $jsonPath);
        }

        $jsonContent = file_get_contents($jsonPath);
        $this->dvhcData = json_decode($jsonContent, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception('Invalid JSON in DVHC file: ' . json_last_error_msg());
        }
    }

    /**
     * Search for old ward names in DVHC data
     * DVHC Structure: Column 0 = Current Province, Column 1 = Current District, Column 2 = Old Ward Names
     *
     * @param string $searchTerm
     * @param string $type - 'province', 'district', or 'ward'
     * @return array
     */
    public function searchInDvhc($searchTerm, $type = 'all')
    {
        $results = [];
        $searchTerm = $this->normalizeSearchTerm($searchTerm);

        foreach ($this->dvhcData['rows'] as $row) {
            $columns = $row['columns'];
            $currentProvinceName = $columns[0] ?? '';  // Current province name
            $currentDistrictName = $columns[1] ?? '';  // Current district name
            $oldWardNames = $columns[2] ?? '';         // Old ward names (comma separated)
            dd("Xã Cam Thủy (huyện Lệ Thủy)");

            // Search in different columns based on type
            switch ($type) {
                case 'province':
                    // Search in current province names (for reference)
                    if ($this->matchesSearchTerm($currentProvinceName, $searchTerm)) {
                        $results[] = [
                            'type' => 'province',
                            'current_province' => $currentProvinceName,
                            'current_district' => $currentDistrictName,
                            'old_wards' => $oldWardNames,
                            'full_row' => $columns
                        ];
                    }
                    break;

                case 'district':
                    // Search in current district names (for reference)
                    if ($this->matchesSearchTerm($currentDistrictName, $searchTerm)) {
                        $results[] = [
                            'type' => 'district',
                            'current_province' => $currentProvinceName,
                            'current_district' => $currentDistrictName,
                            'old_wards' => $oldWardNames,
                            'full_row' => $columns
                        ];
                    }
                    break;

                case 'ward':
                    // Search in old ward names (this is the main use case)
                    if ($this->matchesSearchTerm($oldWardNames, $searchTerm)) {
                        $results[] = [
                            'type' => 'ward',
                            'current_province' => $currentProvinceName,
                            'current_district' => $currentDistrictName,
                            'matching_old_ward' => $this->extractMatchingWard($oldWardNames, $searchTerm),
                            'all_old_wards' => $oldWardNames,
                            'full_row' => $columns
                        ];
                    }
                    break;

                default: // 'all'
                    $matches = [];
                    if ($this->matchesSearchTerm($currentProvinceName, $searchTerm)) {
                        $matches[] = 'current_province';
                    }
                    if ($this->matchesSearchTerm($currentDistrictName, $searchTerm)) {
                        $matches[] = 'current_district';
                    }
                    if ($this->matchesSearchTerm($oldWardNames, $searchTerm)) {
                        $matches[] = 'old_ward';
                    }

                    if (!empty($matches)) {
                        $results[] = [
                            'types' => $matches,
                            'current_province' => $currentProvinceName,
                            'current_district' => $currentDistrictName,
                            'old_wards' => $oldWardNames,
                            'search_term' => $searchTerm,
                            'full_row' => $columns
                        ];
                    }
                    break;
            }
        }

        return $results;
    }

    /**
     * Create AddressConversion records from DVHC search results
     * 
     * @param array $searchResults
     * @param string $searchTerm
     * @return array
     */
    public function createAddressConversions($searchResults, $searchTerm)
    {
        $created = [];
        $errors = [];

        foreach ($searchResults as $result) {
            try {
                // Extract data based on the correct DVHC structure
                $currentProvinceName = $result['current_province'] ?? '';
                $currentDistrictName = $result['current_district'] ?? '';
                $oldWardNames = $result['old_wards'] ?? $result['all_old_wards'] ?? '';

                // Find current administrative units
                $currentProvince = $this->findCurrentProvince($currentProvinceName);
                $currentDistrict = $this->findCurrentDistrict($currentDistrictName, $currentProvinceName);
                $currentWard = $this->findCurrentWardInDistrict($currentDistrictName, $currentProvinceName);

                // Parse individual old ward names from the comma-separated string
                $oldWardList = array_map('trim', explode(',', $oldWardNames));

                foreach ($oldWardList as $oldWardName) {
                    if (empty($oldWardName)) continue;

                    // Clean up ward name (remove prefixes like "Xã", "Thị trấn", etc.)
                    $cleanOldWardName = $this->cleanWardName($oldWardName);

                    $addressConversion = AddressConversion::create([
                        'ward_name' => $oldWardName,  // Original old ward name
                        'district_name' => $currentDistrictName,  // Current district name
                        'province_name' => $currentProvinceName,  // Current province name
                        'detail_address' => $searchTerm,
                        'new_detail' => $currentWard ? $currentWard->full_name : $currentDistrictName,
                        'new_ward' => $currentWard ? $currentWard->full_name : '',
                        'new_ward_code' => $currentWard ? $currentWard->code : '',
                        'new_province' => $currentProvince ? $currentProvince->full_name : $currentProvinceName,
                        'new_province_code' => $currentProvince ? $currentProvince->code : '',
                        'new_full_address' => $this->buildNewFullAddress($currentWard, $currentProvince, $currentDistrict),
                        'not_sure' => !$currentWard,
                        'success' => $currentProvince && $currentDistrict,
                        'ward_code_missing' => !$currentWard,
                        'province_code_missing' => !$currentProvince,
                    ]);

                    $created[] = $addressConversion;
                }
            } catch (\Exception $e) {
                $errors[] = [
                    'result' => $result,
                    'error' => $e->getMessage()
                ];
            }
        }

        return [
            'created' => $created,
            'errors' => $errors,
            'total_created' => count($created),
            'total_errors' => count($errors)
        ];
    }

    /**
     * Normalize search term for better matching
     */
    private function normalizeSearchTerm($term)
    {
        return Str::lower(trim($term));
    }

    /**
     * Check if a text matches the search term
     */
    private function matchesSearchTerm($text, $searchTerm)
    {
        $normalizedText = $this->normalizeSearchTerm($text);
        return Str::contains($normalizedText, $searchTerm);
    }

    /**
     * Extract the specific ward name that matches the search term
     */
    private function extractMatchingWard($wardNames, $searchTerm)
    {
        $wards = array_map('trim', explode(',', $wardNames));

        foreach ($wards as $ward) {
            if ($this->matchesSearchTerm($ward, $searchTerm)) {
                return $ward;
            }
        }

        return $wardNames; // Return all if no specific match
    }

    /**
     * Find current province by name
     */
    private function findCurrentProvince($provinceName)
    {
        // Try exact match first
        $province = Province::where('full_name', $provinceName)->first();

        if (!$province) {
            // Try partial match
            $province = Province::where('full_name', 'like', '%' . $provinceName . '%')->first();
        }

        return $province;
    }

    /**
     * Find current district by name and province
     */
    private function findCurrentDistrict($districtName, $provinceName)
    {
        // Try to find district by name
        $district = District::where('full_name', $districtName)->first();

        if (!$district) {
            // Try partial match
            $district = District::where('full_name', 'like', '%' . $districtName . '%')->first();
        }

        return $district;
    }

    /**
     * Find current ward in the specified district
     */
    private function findCurrentWardInDistrict($districtName, $provinceName)
    {
        // First find the district
        $district = $this->findCurrentDistrict($districtName, $provinceName);

        if ($district) {
            // Get any ward from this district as a representative
            $ward = Ward::where('district_code', $district->code)->first();
            return $ward;
        }

        return null;
    }

    /**
     * Clean ward name by removing common prefixes
     */
    private function cleanWardName($wardName)
    {
        $prefixes = ['Xã ', 'Thị trấn ', 'Phường ', 'Thị xã '];

        foreach ($prefixes as $prefix) {
            if (str_starts_with($wardName, $prefix)) {
                return substr($wardName, strlen($prefix));
            }
        }

        return $wardName;
    }

    /**
     * Build new full address string
     */
    private function buildNewFullAddress($currentWard, $currentProvince, $currentDistrict = null)
    {
        $parts = [];

        if ($currentWard) {
            $parts[] = $currentWard->full_name;

            if ($currentWard->district) {
                $parts[] = $currentWard->district->full_name;
            }
        } elseif ($currentDistrict) {
            $parts[] = $currentDistrict->full_name;
        }

        if ($currentProvince) {
            $parts[] = $currentProvince->full_name;
        }

        return implode(', ', $parts);
    }

    /**
     * Get statistics about DVHC data
     */
    public function getStatistics()
    {
        $totalRows = count($this->dvhcData['rows']);
        $provinces = [];
        $districts = [];
        $totalWards = 0;

        foreach ($this->dvhcData['rows'] as $row) {
            $columns = $row['columns'];
            $provinceName = $columns[0] ?? '';
            $districtName = $columns[1] ?? '';
            $wardNames = $columns[2] ?? '';

            if (!in_array($provinceName, $provinces)) {
                $provinces[] = $provinceName;
            }

            $districtKey = $provinceName . '|' . $districtName;
            if (!in_array($districtKey, $districts)) {
                $districts[] = $districtKey;
            }

            $wardCount = count(array_filter(array_map('trim', explode(',', $wardNames))));
            $totalWards += $wardCount;
        }

        return [
            'total_rows' => $totalRows,
            'unique_provinces' => count($provinces),
            'unique_districts' => count($districts),
            'estimated_total_wards' => $totalWards,
            'provinces_list' => $provinces
        ];
    }
}
