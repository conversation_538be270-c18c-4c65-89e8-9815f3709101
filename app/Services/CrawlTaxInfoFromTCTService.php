<?php

namespace App\Services;

use DOMDocument;
use DOMXPath;
use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\ImageManager;

class CrawlTaxInfoFromTCTService
{
    protected $crawlMSTDN;

    public function __construct()
    {
        $this->crawlMSTDN = new CrawlMSTDN();
    }

    public function handle($tax_id)
    {
        $captcha = $this->crawlMSTDN->getCaptcha();
        $result_image  = base64_encode($captcha);


        $manager = new ImageManager(new Driver());
        $imageWhite = $manager->create(130, 50)->fill('ffffff');
        $imageWhiteInsert = $imageWhite->place($result_image, 'center');
        $dataUri = $imageWhiteInsert->toJpeg()->toDataUri();
        $base64 = explode(",", $dataUri)[1];
        $resolvedCaptcha = $this->crawlMSTDN->convertCaptcha($base64);

        if ($resolvedCaptcha && $resolvedCaptcha->captcha) {
            $r = $this->crawlMSTDN->searchMST($tax_id, $resolvedCaptcha->captcha);
            $baseinfo = $this->parseTableListToJSON($r);

            if (isset($baseinfo['status']) && $baseinfo['status'] == 'notfound') {
                return $baseinfo;
            }
            if (!isset($baseinfo['taxid'])) {
                return false;
            }
            $result = $this->crawlMSTDN->searchMSTDetail($baseinfo['taxid']);
            $item = $this->parseTableToJSON($result);

            $item['legal_representative_id'] = $baseinfo['id_number'];
            $item['note'] = $baseinfo['note'];
            if ($item) {
                $html_Industry = $this->crawlMSTDN->getNghanhNghe($item['taxid']);
                $array_Industry = $this->parseTableIndustry($html_Industry);

                $html_loaithue = $this->crawlMSTDN->getLoaiThue($item['taxid']);
                $array_loaithue = $this->parseTableLoaiThue($html_loaithue);
                $listNganh = [];

                $listNganh = $this->mapIndustry($array_Industry);

                $item['industries'] = $listNganh;
                $item['tax_type'] = $array_loaithue;

                $item['phone'] = $this->parsePhoneNumber($result);
            }

            return $item;
        }

        return false;
    }

    public function parseTableListToJSON($htmlContent)
    {
        // Load the HTML content
        $dom = new DOMDocument();
        $htmlContent = str_ireplace('<head>', '<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8" />', $htmlContent);
        @$dom->loadHTML($htmlContent);

        $xpath = new DOMXPath($dom);

        // Search for the text "Vui lòng nhập đúng mã xác nhận!"
        $text = "Vui lòng nhập đúng mã xác nhận";
        $nodes = $xpath->query("//*[contains(text(), '$text')]");
        // Check if any nodes contain the specified text
        if ($nodes->length > 0) {
            return ['captcha' => false];
        }



        // Locate table rows
        $rows = $xpath->query('//table[@class="ta_border"]/tr');

        // Loop through each row, skipping the header row
        foreach ($rows as $index => $row) {
            // Skip the header row and rows with colspan for pagination or notes
            if ($index === 0) {
                continue;
            }


            $cells = $xpath->query('td | th', $row);

            if (strpos(trim($cells->item(0)->textContent), 'Không tìm thấy') !== false) {
                return ['status' => 'notfound', 'message' => trim($cells->item(0)->textContent)];
            }


            if ($index === 1) {

                $cells = $xpath->query('td | th', $row);





                return [
                    // 'STT' => trim($cells->item(0)->textContent),
                    'taxid' => trim($cells->item(1)->textContent),
                    'tax_name' => preg_replace('/\s+/', ' ', trim($cells->item(2)->textContent)),
                    'tax_authority' => trim($cells->item(3)->textContent),
                    'id_number' => trim($cells->item(4)->textContent),
                    'last_updated' => trim($cells->item(5)->textContent),
                    'note' => trim($cells->item(6)->textContent),
                ];
            }
        }

        return false;
    }

    public function parseTableToJSON($htmlContent)
    {
        $dom = new DOMDocument();
        $htmlContent = str_ireplace('<head>', '<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8" />', $htmlContent);
        @$dom->loadHTML($htmlContent);
        $xpath = new DOMXPath($dom);

        $table = $xpath->query("//table[contains(@class, 'ta_border')]")->item(0);

        if (!$table) {
            return false;
        }

        $jsonData = [];
        foreach ($table->getElementsByTagName('tr') as $row) {
            $headers = $row->getElementsByTagName('th');
            $cells = $row->getElementsByTagName('td');

            for ($i = 0; $i < $headers->length; $i++) {
                $key = trim($headers->item($i)->textContent);
                $value = $cells->item($i) ? trim($cells->item($i)->textContent) : null;

                if ($key && $value !== null) {
                    $jsonData[$key] = $value;
                }
            }
            // Mảng ánh xạ từ tiếng Việt sang tiếng Anh
            $mapping = [
                "Mã số doanh nghiệp" => "taxid",
                "Ngày cấp" => "date_of_issue",
                "Ngày đóng MST" => "date_of_tax_closure",
                "Tên chính thức" => "official_name",
                "Tên giao dịch" => "trading_name",
                "Nơi đăng ký quản lý thuế" => "tax_management_office",
                "Địa chỉ trụ sở" => "head_office_address",
                "Nơi đăng ký nộp thuế" => "tax_registration_office",
                "Địa chỉ nhận thông báo thuế" => "tax_notification_address",
                "QĐTL-Ngày cấp" => "decision_date",
                "Cơ quan ra quyết định" => "issuing_authority",
                "GPKD-Ngày cấp" => "business_license_issue_date",
                "Cơ quan cấp" => "issuing_authority",
                "Ngày nhận tờ khai" => "declaration_receipt_date",
                "Ngày/tháng bắt đầu năm tài chính" => "fiscal_year_start",
                "Ngày/tháng kết thúc năm tài chính" => "fiscal_year_end",
                "Mã số hiện thời" => "current_taxid",
                "Ngày bắt đầu HĐ" => "start_date",
                "Chương – Khoản" => "program_section",
                "Hình thức h.toán" => "accounting_method",
                "PP tính thuế GTGT" => "vat_calculation_method",
                "Chủ sở hữu/Người đại diện pháp luật" => "legal_representative",
                "Địa chỉ chủ sở hữu/người đại diện pháp luật" => "legal_representative_address",
                "Tên giám đốc" => "director_name",
                "Địa Chỉ" => "director_address",
                "Kế toán trưởng" => "chief_accountant"
            ];

            // Đổi tên khóa từ tiếng Việt sang tiếng Anh
            $translatedData = [];
            foreach ($jsonData as $key => $value) {
                $translatedKey = $mapping[$key] ?? strtolower(str_replace(' ', '_', $key)); // Sử dụng tên đã ánh xạ hoặc chuyển đổi

                if ($translatedKey === "business_license_issue_date" && strpos($value, '-') !== false) {
                    // Tách giá trị thành business_license và issue_date
                    list($businessLicense, $issueDate) = array_map('trim', explode('-', $value, 2));
                    $translatedData["business_license"] = $businessLicense;
                    $translatedData["issue_date"] = $issueDate;
                } else {
                    $translatedData[$translatedKey] = $value;
                }
            }
        }
        return $translatedData;
    }

    public function mapIndustry($listIndustry)
    {
        if (empty($listIndustry)) {
            return [];
        }

        $list = [];
        foreach ($listIndustry as $industry) {
            $array = explode("-", $industry);

            preg_match('/\d{1,}/', $array[0], $matches);
            $code = !empty($matches) ? $matches[0] : null;
            if (!$code) {
                continue;
            }

            $list[] = [
                'code' => $code,
                'primary' => $array[1] == 'Y',
            ];
        }
        return $list;
    }


    public function parseTableLoaiThue($htmlContent)
    {
        $dom = new DOMDocument();
        $htmlContent = '<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8" /></head>' . $htmlContent;

        @$dom->loadHTML($htmlContent);
        $xpath = new DOMXPath($dom);
        $table = $xpath->query("//table[contains(@class, 'ta_border')]")->item(0);
        if (!$table) {
            return false;
        }
        $jsonData = [];
        $rows = $table->getElementsByTagName('tr');


        foreach ($rows as $index => $row) {
            if ($index === 0) continue; // Bỏ qua hàng tiêu đề

            $cells = $row->getElementsByTagName('td');
            if ($cells->length > 0) {
                $data = [
                    "Ma" => str_replace('.', '', trim($cells->item(1)->textContent)),
                    "Ten" => trim($cells->item(2)->textContent),
                ];
                $jsonData[] = $data;
            }
        }

        return $jsonData;
    }

    public function parseTableIndustry($htmlContent)
    {
        $dom = new DOMDocument();
        $htmlContent = '<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8" /></head>' . $htmlContent;

        @$dom->loadHTML($htmlContent);
        $xpath = new DOMXPath($dom);
        $table = $xpath->query("//table[contains(@class, 'ta_border')]")->item(0);
        if (!$table) {
            return false;
        }
        $jsonData = [];
        $rows = $table->getElementsByTagName('tr');


        foreach ($rows as $index => $row) {
            if ($index === 0) continue; // Bỏ qua hàng tiêu đề

            $cells = $row->getElementsByTagName('td');
            if ($cells->length > 0) {
                $data = [
                    "Ma" => str_replace('.', '', trim($cells->item(1)->textContent)),
                    "Ten" => trim($cells->item(2)->textContent),
                    "NNKD_Chinh" => trim($cells->item(3)->textContent) ? 'Y' : 'N'
                ];

                //Lưu  ngành nghề mới chưa có DB
                // $nghe = Industry::where('code', substr($data['Ma'], 1))->first();

                // if (!$nghe) {
                // Industry::create(['code' => substr($data['Ma'], 1), 'name' => $data['Ten']]);
                // }
                array_push($jsonData, $data['Ma'] . '-' . $data['NNKD_Chinh']);
            }
        }

        return $jsonData;
    }

    public function parsePhoneNumber($htmlContent)
    {
        $phone = null;
        preg_match('/<!--\s*(<tr>[\s\S]*?<\/tr>)\s*-->/', $htmlContent, $matches);
        if (!empty($matches[1])) {
            $commentedHtml = $matches[1];
            if (str_contains($commentedHtml, "Điện thoại/Fax")) {
                $dom = new DOMDocument();
                $htmlContent = '<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8" /></head>' . $commentedHtml;

                @$dom->loadHTML($htmlContent);
                $xpath = new DOMXPath($dom);

                // Tìm thẻ <th> có nội dung "Điện thoại/Fax"
                $nodes = $xpath->query("//th[contains(text(), 'Điện thoại/Fax')]");

                if ($nodes->length > 0) {
                    $td = $nodes->item(0)->nextSibling->nextSibling; // Lấy <td> kế bên <th>
                    if ($td) {
                        $phone = $td->textContent;
                    }
                }
            }
        }

        return $phone;
    }
}
