<?php

namespace App\Services;

use DOMDocument;
use DOMXPath;
use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\ImageManager;

class CrawlTaxPersonalFromTCTService
{
    protected $crawlMSTDN;

    public function __construct()
    {
        $this->crawlMSTDN = new CrawlMSTDN();
    }

    public function handle($textSearch)
    {
        $flag1 = true;
        $flag2 = true;
        $captcha = $this->crawlMSTDN->getCaptcha();
        $result_image  = base64_encode($captcha);
        $manager = new ImageManager(new Driver());
        $imageWhite = $manager->create(130, 50)->fill('ffffff');
        $imageWhiteInsert = $imageWhite->place($result_image, 'center');
        $dataUri = $imageWhiteInsert->toJpeg()->toDataUri();
        $base64 = explode(",", $dataUri)[1];
        // $resolvedCaptcha = $this->crawlMSTDN->convertCaptcha($base64);
        $resolvedCaptcha = $this->crawlMSTDN->autoCaptchaPro($base64);

        if ($resolvedCaptcha && $resolvedCaptcha->captcha) {
            $r = $this->crawlMSTDN->searchMSTPersonal($textSearch, $resolvedCaptcha->captcha, true);
            $baseinfo1 = $this->parseTableListToJSON($r);

            if (isset($baseinfo1['status']) && $baseinfo1['status'] == 'notfound') {
                $flag1 = false;
            }
            if (!isset($baseinfo1['taxid'])) {
                $flag1 = false;
            }
            // $result = $this->crawlMSTDN->searchMSTPersonalDetail($baseinfo['taxid'], $baseinfo['id_number']);
            // $item1 = $this->parseTableToJSON($result);
        }

        $captcha = $this->crawlMSTDN->getCaptcha();
        $result_image  = base64_encode($captcha);
        $manager = new ImageManager(new Driver());
        $imageWhite = $manager->create(130, 50)->fill('ffffff');
        $imageWhiteInsert = $imageWhite->place($result_image, 'center');
        $dataUri = $imageWhiteInsert->toJpeg()->toDataUri();
        $base64 = explode(",", $dataUri)[1];
        // $resolvedCaptcha = $this->crawlMSTDN->convertCaptcha($base64);
        $resolvedCaptcha = $this->crawlMSTDN->autoCaptchaPro($base64);

        if ($resolvedCaptcha && $resolvedCaptcha->captcha) {
            $r = $this->crawlMSTDN->searchMSTPersonal($textSearch, $resolvedCaptcha->captcha, false);
            $baseinfo2 = $this->parseTableListToJSON($r);

            if (isset($baseinfo2['status']) && $baseinfo2['status'] == 'notfound') {
                $flag2 = false;
            }
            if (!isset($baseinfo2['taxid'])) {
                $flag2 = false;
            }
            // $result = $this->crawlMSTDN->searchMSTPersonalDetail($baseinfo['taxid'], $baseinfo['id_number']);
            // $item2 = $this->parseTableToJSON($result);
        }
        if ($flag1) {
            // $result = $this->crawlMSTDN->searchMSTPersonalDetail($baseinfo1['taxid'], $baseinfo1['id_number']);
            // $item = $this->parseTableToJSON($result);
            // return $item;
            return $baseinfo1;
        }
        if ($flag2) {
            // $result = $this->crawlMSTDN->searchMSTPersonalDetail($baseinfo2['taxid'], $baseinfo2['id_number']);
            // $item = $this->parseTableToJSON($result);
            // return $item;
            return $baseinfo2;
        }

        return false;
    }

    public function parseTableListToJSON($htmlContent)
    {
        // Load the HTML content
        $dom = new DOMDocument();
        $htmlContent = str_ireplace('<head>', '<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8" />', $htmlContent);
        @$dom->loadHTML($htmlContent);

        $xpath = new DOMXPath($dom);

        // Search for the text "Vui lòng nhập đúng mã xác nhận!"
        $text = "Vui lòng nhập đúng mã xác nhận";
        $nodes = $xpath->query("//*[contains(text(), '$text')]");
        // Check if any nodes contain the specified text
        if ($nodes->length > 0) {
            return ['captcha' => false];
        }



        // Locate table rows
        $rows = $xpath->query('//table[@class="ta_border"]/tr');

        // Loop through each row, skipping the header row
        foreach ($rows as $index => $row) {
            // Skip the header row and rows with colspan for pagination or notes
            if ($index === 0) {
                continue;
            }

            $cells = $xpath->query('td | th', $row);

            if (strpos(trim($cells->item(0)->textContent), 'Không tìm thấy người nộp thuế nào phù hợp.') !== false) {
                return ['status' => 'notfound', 'message' => trim($cells->item(0)->textContent)];
            }

            // Tìm tất cả thẻ <script>
            $scripts = $xpath->query('//script');

            $nntJson = null;

            foreach ($scripts as $script) {
                $content = $script->textContent;

                // Tìm chuỗi bắt đầu với var nntJson = { ... };
                if (preg_match('/var\s+nntJson\s*=\s*(\{.*?\});/s', $content, $matches)) {
                    $jsonString = $matches[1];

                    // Decode thành mảng PHP
                    $nntJson = json_decode($jsonString, true);
                    break;
                }
            }

            if ($nntJson && isset($nntJson['status']) && $nntJson['status'] == 1 && !empty($nntJson['DATA'])) {
                $firstData = $nntJson['DATA'][0];
                $findDKT2080 = array_filter($firstData['DKT_GIAY_TO'], function ($item) {
                    return $item['LOAI'] == '2080';
                });
                $getDKT2080 = reset($findDKT2080);

                $findDKT1000 = array_filter($firstData['DKT_GIAY_TO'], function ($item) {
                    return $item['LOAI'] == '1000';
                });
                $getDKT1000 = reset($findDKT1000);

                $findDKT_DIA_CHIXXDEFAULT = array_filter($firstData['DKT_DIA_CHI'], function ($item) {
                    return $item['LOAI'] == 'XXDEFAULT';
                });
                $getDKT_DIA_CHIXXDEFAULT = reset($findDKT_DIA_CHIXXDEFAULT);
                return [
                    'taxid' => $nntJson['DATA'][0]['MST'],
                    "name" => $nntJson['DATA'][0]['TEN_NNT'],
                    "province" => $getDKT_DIA_CHIXXDEFAULT['TINH_TP'],
                    "district" => $getDKT_DIA_CHIXXDEFAULT['DIA_CHI'] . ', ' . $getDKT_DIA_CHIXXDEFAULT['PHUONG_XA'] . ', ' . $getDKT_DIA_CHIXXDEFAULT['QUAN_HUYEN'],
                    "phone" => '',
                    "issue_date" => $getDKT1000['NGAY_CAP'],
                    "close_date" => $getDKT1000['NGAY_HL_DEN'] ?? '',

                    'tax_management_office' => $nntJson['DATA'][0]['TEN_CQT_QLY'],
                    'id_number' => $getDKT2080['SO_GIAY_TO'],
                    'last_updated' => $nntJson['DATA'][0]['MST'],
                    'note' => $nntJson['DATA'][0]['TEN_TRANG_THAI'],
                    'head_office_address' => '',
                ];
            }
            return ['status' => 'notfound', 'message' => 'Không tìm thấy thông tin'];


            // if ($index == 1) {


            //     $cells = $xpath->query('td | th', $row);
            //     return [
            //         'taxid' => trim($cells->item(1)->textContent),
            //         'tax_name' => preg_replace('/\s+/', ' ', trim($cells->item(2)->textContent)),
            //         'tax_authority' => trim($cells->item(3)->textContent),
            //         'id_number' => trim($cells->item(4)->textContent),
            //         'last_updated' => trim($cells->item(5)->textContent),
            //         'note' => trim($cells->item(6)->textContent),
            //     ];
            // }
        }

        return false;
    }

    public function parseTableToJSON($htmlContent)
    {
        $dom = new DOMDocument();
        $htmlContent = str_ireplace('<head>', '<head><meta http-equiv="Content-Type" content="text/html; charset=utf-8" />', $htmlContent);
        @$dom->loadHTML($htmlContent);
        $xpath = new DOMXPath($dom);

        $table = $xpath->query("//table[contains(@class, 'ta_border')]")->item(0);

        if (!$table) {
            return false;
        }

        $jsonData = [];
        foreach ($table->getElementsByTagName('tr') as $row) {
            $headers = $row->getElementsByTagName('th');
            $cells = $row->getElementsByTagName('td');

            for ($i = 0; $i < $headers->length; $i++) {
                $key = trim($headers->item($i)->textContent);
                $value = $cells->item($i) ? trim($cells->item($i)->textContent) : null;

                if ($key && $value !== null) {
                    $jsonData[$key] = $value;
                }
            }
            $mapping = [
                "Mã số thuế" => "taxid",
                "Tên người nộp thuế" => "name",
                "Số CMT/Thẻ căn cước" => "id_number",
                "Nơi đăng ký quản lý" => "tax_management_office",
                "Địa chỉ trụ sở" => "head_office_address",
                "Tỉnh/TP" => "province",
                "Quận/Huyện" => "district",
                "Điện thoại" => "phone",
                "Ngày cấp MST" => "issue_date",
                "Ngày đóng MST" => "close_date",
                "Ghi chú" => "note",
            ];
            $translatedData = [];
            foreach ($jsonData as $key => $value) {
                $translatedKey = $mapping[$key] ?? strtolower(str_replace(' ', '_', $key)); // Sử dụng tên đã ánh xạ hoặc chuyển đổi

                $translatedData[$translatedKey] = $value;
            }
        }
        return $translatedData;
    }
}
