<?php

namespace App\Services;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class CrawlMSTDN
{
    protected $client;

    public function __construct()
    {
        $this->client = new Client(['cookies' => true]);
    }

    public function generateRandomString($length = 10)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }

    public function getCaptcha()
    {
        $headers = [
            'accept' => 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'accept-encoding' => 'gzip, deflate, br, zstd',
            'accept-language' => 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7',
            'connection' => 'keep-alive',
            'Host' => 'tracuunnt.gdt.gov.vn',
            'Origin' => 'https://tracuunnt.gdt.gov.vn',
            'Referer' => 'https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp',
            'Sec-Fetch-Dest' => 'document',
            'Sec-Fetch-Mode' => 'navigate',
            'Sec-Fetch-Site' => 'same-origin',
            'Sec-Fetch-User' => '?1',
            'Upgrade-Insecure-Requests' => '1',
            'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
            'sec-ch-ua' => '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-platform' => '"macOS"'
        ];
        $result = $this->callApi($headers, 'https://tracuunnt.gdt.gov.vn/tcnnt/captcha.png?uid=d094105c-' . $this->generateRandomString(4) . '-' . $this->generateRandomString(4) . '-8b2e-098c186a7846', 'GET');

        return $result ?? false;
    }

    public function searchMST($mst, $captcha)
    {
        $headers = [
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding' => 'gzip, deflate, br, zstd',
            'Accept-Language' => 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'Content-Length' => '81',
            'Content-Type' => 'application/x-www-form-urlencoded',
            'Host' => 'tracuunnt.gdt.gov.vn',
            'Origin' => 'https://tracuunnt.gdt.gov.vn',
            'Referer' => 'https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp',
            'Sec-Fetch-Dest' => 'document',
            'Sec-Fetch-Mode' => 'navigate',
            'Sec-Fetch-Site' => 'same-origin',
            'Sec-Fetch-User' => '?1',
            'Upgrade-Insecure-Requests' => '1',
            'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
            'sec-ch-ua' => '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-platform' => '"macOS"'
        ];

        $data = [
            'action' => 'action',
            'id' => $mst,
            'page' => '1',
            'mst' => $mst,
            'fullname' => '',
            'address' => '',
            'cmt' => '',
            'captcha' => $captcha
        ];

        $result = $this->callApi($headers, "https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp?action=action&id=&page=1&mst=$mst&fullname=&address=&cmt=&captcha=$captcha", 'POST', $data);
        //Vui lòng nhập đúng mã xác nhận!


        return $result ?? false;
    }


    public function searchMSTDetail($mst)
    {
        $headers = [
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding' => 'gzip, deflate, br, zstd',
            'Accept-Language' => 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'Content-Length' => '82',
            'Content-Type' => 'application/x-www-form-urlencoded',
            'Host' => 'tracuunnt.gdt.gov.vn',
            'Origin' => 'https://tracuunnt.gdt.gov.vn',
            'Referer' => 'https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp',
            'Sec-Fetch-Dest' => 'document',
            'Sec-Fetch-Mode' => 'navigate',
            'Sec-Fetch-Site' => 'same-origin',
            'Sec-Fetch-User' => '?1',
            'Upgrade-Insecure-Requests' => '1',
            'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
            'sec-ch-ua' => '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-platform' => '"macOS"'
        ];

        $data = [
            'action' => 'action',
            'id' => $mst,
            'page' => '1',
            'mst' => $mst,
            'fullname' => '',
            'address' => '',
            'cmt' => '',
            'captcha' => ''
        ];

        $result = $this->callApi($headers, "https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp?action=action&id=$mst&page=1&mst=$mst&fullname=&address=&cmt=&captcha=", 'POST', $data);

        return $result ?? false;
    }


    public function getNghanhNghe($mst)
    {
        $headers = [
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding' => 'gzip, deflate, br, zstd',
            'Accept-Language' => 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'Content-Length' => '14',
            'Content-Type' => 'application/x-www-form-urlencoded',
            'Host' => 'tracuunnt.gdt.gov.vn',
            'Origin' => 'https://tracuunnt.gdt.gov.vn',
            'Referer' => 'https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp',
            'Sec-Fetch-Dest' => 'document',
            'Sec-Fetch-Mode' => 'navigate',
            'Sec-Fetch-Site' => 'same-origin',
            'Sec-Fetch-User' => '?1',
            'Upgrade-Insecure-Requests' => '1',
            'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
            'sec-ch-ua' => '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-platform' => '"macOS"'
        ];

        $data = [
            'tin' => $mst
        ];

        $result = $this->callApi($headers, "https://tracuunnt.gdt.gov.vn/tcnnt/nganhkinhdoanh.jsp?tin=$mst", 'POST', $data);

        return $result ?? false;
    }



    public function getLoaiThue($mst)
    {
        $headers = [
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding' => 'gzip, deflate, br, zstd',
            'Accept-Language' => 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'Content-Length' => '14',
            'Content-Type' => 'application/x-www-form-urlencoded',
            'Host' => 'tracuunnt.gdt.gov.vn',
            'Origin' => 'https://tracuunnt.gdt.gov.vn',
            'Referer' => 'https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp',
            'Sec-Fetch-Dest' => 'document',
            'Sec-Fetch-Mode' => 'navigate',
            'Sec-Fetch-Site' => 'same-origin',
            'Sec-Fetch-User' => '?1',
            'Upgrade-Insecure-Requests' => '1',
            'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
            'sec-ch-ua' => '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-platform' => '"macOS"'
        ];

        $data = [
            'tin' => $mst
        ];

        $result = $this->callApi($headers, "https://tracuunnt.gdt.gov.vn/tcnnt/loaithue.jsp?tin=$mst", 'POST', $data);

        return $result ?? false;
    }

    public function autoCaptchaPro($base64)
    {
        $client = new Client();
        try {
            $res = $client->request('POST', 'https://autocaptcha.pro/apiv3/process', [
                'json' => array(
                    'key' => 'e702de98c322ca0d9b35039579b22cce',
                    'type' => 'imagetotext',
                    'module' => 'common',
                    'casesensitive' => false,
                    'img' => $base64
                )
            ]);
            return json_decode($res->getBody());
        } catch (\Throwable $th) {
        }
        return false;
    }

    public function convertCaptcha($base64)
    {
        $client = new Client();
        try {
            $res = $client->request('POST', 'http://103.142.26.15:8122/api/captcha/tracuuthue', [
                'json' => array(
                    'api_key' => "34269d96a9369e2fdd64601ac52e1c3c",
                    'base64' => $base64
                )
            ]);
            return json_decode($res->getBody());
        } catch (\Throwable $th) {
        }
        return false;
    }


    private function callApi($headers, $endpoint, $method = 'GET', $data = [])
    {
        try {

            $response = $this->client->request($method, $endpoint, [
                'form_params' => $data,
                'headers' => $headers,
                'verify' => false,
            ]);

            return $response->getBody()->getContents();
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            $response = json_decode($e->getResponse()->getBody()->getContents(), true);
            Log::error($e);
            return false;
        }
    }

    public function searchMSTPersonal($textSearch, $captcha, $isTax = false)
    {
        $headers = [
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding' => 'gzip, deflate, br, zstd',
            'Accept-Language' => 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'Content-Length' => '81',
            'Content-Type' => 'application/x-www-form-urlencoded',
            'Host' => 'tracuunnt.gdt.gov.vn',
            'Origin' => 'https://tracuunnt.gdt.gov.vn',
            'Referer' => 'https://tracuunnt.gdt.gov.vn/tcnnt/mstcn.jsp',
            'Sec-Fetch-Dest' => 'document',
            'Sec-Fetch-Mode' => 'navigate',
            'Sec-Fetch-Site' => 'same-origin',
            'Sec-Fetch-User' => '?1',
            'Upgrade-Insecure-Requests' => '1',
            'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
            'sec-ch-ua' => '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-platform' => '"macOS"'
        ];

        $data = [
            'cm' => 'cm',
            'mst' => $isTax ? $textSearch : '',
            'cmt' => !$isTax ? $textSearch : '',
            'captcha' => $captcha
        ];

        $result = $this->callApi($headers, "https://tracuunnt.gdt.gov.vn/tcnnt/mstcn.jsp", 'POST', $data);
        //Vui lòng nhập đúng mã xác nhận!


        return $result ?? false;
    }

    public function searchMSTPersonalDetail($tax_id, $id_number)
    {
        $headers = [
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding' => 'gzip, deflate, br, zstd',
            'Accept-Language' => 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'Content-Length' => '82',
            'Content-Type' => 'application/x-www-form-urlencoded',
            'Host' => 'tracuunnt.gdt.gov.vn',
            'Origin' => 'https://tracuunnt.gdt.gov.vn',
            'Referer' => 'https://tracuunnt.gdt.gov.vn/tcnnt/mstdn.jsp',
            'Sec-Fetch-Dest' => 'document',
            'Sec-Fetch-Mode' => 'navigate',
            'Sec-Fetch-Site' => 'same-origin',
            'Sec-Fetch-User' => '?1',
            'Upgrade-Insecure-Requests' => '1',
            'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
            'sec-ch-ua' => '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-platform' => '"macOS"'
        ];

        $data = [
            'id' => $tax_id,
            'action' => 'action',
            'page' => '1',
            'mst1' => $tax_id,
            'captcha' => ''
        ];

        $result = $this->callApi($headers, "https://tracuunnt.gdt.gov.vn/tcnnt/mstcn.jsp", 'POST', $data);

        return $result ?? false;
    }
}
