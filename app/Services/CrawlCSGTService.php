<?php

namespace App\Services;

use App\Models\VehicleNumber;
use App\Models\Violation;
use DOMDocument;
use DOMXPath;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use TwoCaptcha\TwoCaptcha;

class CrawlCSGTService
{
    protected $client;

    public function __construct()
    {
        $this->client = new Client(['cookies' => true]);
    }

    public function getCaptcha()
    {
        $headers = [
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding' => 'gzip, deflate, br, zstd',
            'Accept-Language' => 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7',
            'Connection' => 'keep-alive',
            'cache-control' => 'no-cache',
            'Content-Type' => 'application/x-www-form-urlencoded',
            'Cookie' => '_ga=GA1.1.649233388.1731466808; PHPSESSID=4hn1vul94a8h2mfv9qn30timn7; _gtpk_testcookie..undefined=1; _gtpk_ref.4061.2d24=%5B%22%22%2C%22%22%2C1739849252%2C%22https%3A%2F%2Fwww.google.com%2F%22%5D; _gtpk_ses.4061.2d24=1; _gtpk_id.4061.2d24=3e086f31f2a61dda.1731466808.5.1739849672.1739849252.; _ga_LHSBE18PPX=GS1.1.1739849251.5.1.1739849821.0.0.0; PHPSESSID=ii91mgtq81p31tqnjsr5uicvg0',
            'Host' => 'www.csgt.vn',
            'Origin' => 'https://www.csgt.vn',
            'Referer' => 'https://www.csgt.vn/',
            'Sec-Fetch-Dest' => 'empty',
            'Sec-Fetch-Mode' => 'cors',
            'Sec-Fetch-Site' => 'same-origin',
            'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'X-Requested-With' => 'XMLHttpRequest',
            'sec-ch-ua' => '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-mobile' => '?0',
            'sec-ch-ua-platform' => '"macOS"'
        ];
        $result = $this->callApi($headers, 'https://www.csgt.vn/lib/captcha/captcha.class.php', 'GET');

        return $result ?? false;
    }

    public function phatnguoi($bks)
    {
        $captcha = $this->getCaptcha();
        if (!$captcha) {
            return [
                'status' => false,
            ];
        }
        $base64  = base64_encode($captcha);
        $resolvedCaptcha = $this->convertCaptcha($base64);

        if ($resolvedCaptcha && $resolvedCaptcha->captcha) {
            $headers = [
                'Accept' => '*/*',
                'Accept-Encoding' => 'gzip, deflate, br, zstd',
                'Accept-Language' => 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7',
                'Connection' => 'keep-alive',
                'Content-Length' => '60',
                'Content-Type' => 'application/x-www-form-urlencoded',
                'Cookie' => '_ga=GA1.1.649233388.1731466808; PHPSESSID=4hn1vul94a8h2mfv9qn30timn7; _gtpk_testcookie..undefined=1; _gtpk_ref.4061.2d24=%5B%22%22%2C%22%22%2C1739849252%2C%22https%3A%2F%2Fwww.google.com%2F%22%5D; _gtpk_ses.4061.2d24=1; _gtpk_id.4061.2d24=3e086f31f2a61dda.1731466808.5.1739849672.1739849252.; _ga_LHSBE18PPX=GS1.1.1739849251.5.1.1739849821.0.0.0; PHPSESSID=ii91mgtq81p31tqnjsr5uicvg0',
                'Host' => 'www.csgt.vn',
                'Origin' => 'https://www.csgt.vn',
                'Referer' => 'https://www.csgt.vn/',
                'Sec-Fetch-Dest' => 'empty',
                'Sec-Fetch-Mode' => 'cors',
                'Sec-Fetch-Site' => 'same-origin',
                'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'X-Requested-With' => 'XMLHttpRequest',
                'sec-ch-ua' => '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
                'sec-ch-ua-mobile' => '?0',
                'sec-ch-ua-platform' => '"macOS"'
            ];

            $data = [
                'BienKS' => $bks,
                'Xe' => '1',
                'captcha' => $resolvedCaptcha->captcha,
                'ipClient' => '********',
                'cUrl' => 'https://www.csgt.vn/'
            ];

            $response = $this->callApi($headers, "https://www.csgt.vn/?mod=contact&task=tracuu_post&ajax", 'POST', $data);
            $result = json_decode($response, true);
            if ($result && isset($result['href'])) {
                $headers = [
                    'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                    'Accept-Encoding' => 'gzip, deflate, br, zstd',
                    'Accept-Language' => 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7',
                    'cache-control' => 'no-cache',
                    'Connection' => 'keep-alive',
                    'Cookie' => '_ga=GA1.1.649233388.1731466808; PHPSESSID=4hn1vul94a8h2mfv9qn30timn7; _gtpk_testcookie..undefined=1; _gtpk_ref.4061.2d24=%5B%22%22%2C%22%22%2C1739849252%2C%22https%3A%2F%2Fwww.google.com%2F%22%5D; _gtpk_ses.4061.2d24=1; _gtpk_id.4061.2d24=3e086f31f2a61dda.1731466808.5.1739849672.1739849252.; _ga_LHSBE18PPX=GS1.1.1739849251.5.1.1739849821.0.0.0; PHPSESSID=ii91mgtq81p31tqnjsr5uicvg0',
                    'Host' => 'www.csgt.vn',
                    'Origin' => 'https://www.csgt.vn',
                    'Referer' => 'https://www.csgt.vn/',
                    'Sec-Fetch-Dest' => 'empty',
                    'Sec-Fetch-Mode' => 'cors',
                    'Sec-Fetch-Site' => 'same-origin',
                    'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'X-Requested-With' => 'XMLHttpRequest',
                    'sec-ch-ua' => '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
                    'sec-ch-ua-mobile' => '?0',
                    'sec-ch-ua-platform' => '"macOS"'
                ];
                $resultDetail = $this->callApi($headers, $result['href'], 'GET');
                $dom = new DOMDocument();
                @$dom->loadHTML($resultDetail);

                $xpath = new DOMXPath($dom);

                // Tìm tất cả các trường hợp vi phạm
                $violations = [];
                $formGroups = $xpath->query('//div[@id="bodyPrint123"]/div[@class="form-group"]');

                $currentViolation = [];

                foreach ($formGroups as $group) {
                    $label = $xpath->query('.//label/span', $group);
                    $value = $xpath->query('.//div[@class="col-md-9"] | .//span[@class="badge"]', $group);


                    if ($label->length > 0 && $value->length > 0) {
                        $labelText = trim($label->item(0)->nodeValue);
                        $valueText = trim($value->item(0)->nodeValue);

                        switch ($labelText) {
                            case 'Biển kiểm soát:':
                                if (!empty($currentViolation)) {
                                    $violations[] = $currentViolation;
                                    $currentViolation = [];
                                }
                                $currentViolation['vehicle_number'] = $valueText;
                                break;
                            case 'Màu biển:':
                                $currentViolation['color'] = $valueText;
                                break;
                            case 'Loại phương tiện:':
                                $currentViolation['vehicle_type'] = $valueText;
                                break;
                            case 'Thời gian vi phạm:':
                                $currentViolation['time'] = now()->createFromFormat('H:i, d/m/Y', $valueText)->format('Y-m-d H:i:s');
                                break;
                            case 'Địa điểm vi phạm:':
                                $currentViolation['location'] = $valueText;
                                break;
                            case 'Hành vi vi phạm:':
                                $currentViolation['violation'] = $valueText;
                                break;
                            case 'Trạng thái:':
                                $currentViolation['status'] = $valueText;
                                break;
                            case 'Đơn vị phát hiện vi phạm:':
                                $currentViolation['detection_unit'] = $valueText;
                                break;
                            case 'Nơi giải quyết vụ việc:':

                                $currentViolation['place_of_solution'] = [];
                                break;
                        }
                    } else {
                        $currentViolation['place_of_solution'][] = $group->textContent;
                    }
                }

                // Thêm trường hợp vi phạm cuối cùng nếu có
                if (!empty($currentViolation)) {
                    $violations[] = $currentViolation;
                }

                // Hiển thị dữ liệu
                if ($violations && !empty($violations)) {
                    $vehicle = VehicleNumber::where('number', $bks)->first();
                    if (!$vehicle) {
                        $vehicle = VehicleNumber::create([
                            'number' => $bks,
                        ]);
                    }
                    foreach ($violations as $violation) {
                        $exist = Violation::where('vehicle_id', $vehicle->id)->where('time', $violation['time'])->first();
                        if (!$exist) {
                            Violation::create([
                                ...$violation,
                                'vehicle_id' => $vehicle->id,
                            ]);
                        } else {
                            $exist->update($violation);
                        }
                    }

                    return [
                        'status' => true,
                        'vehicle_number' => $bks,
                        'vehicle_id' => $vehicle->id,
                    ];
                }
                return [
                    'status' => false,
                    'empty' => true,
                ];
            }


            return [
                'status' => false,
            ];
        }
    }

    public function autoCaptchaPro($base64)
    {
        $client = new Client();
        try {
            $res = $client->request('POST', 'https://autocaptcha.pro/apiv3/process', [
                'json' => array(
                    'key' => '83894a700e36e88f3fc7a1876e6072a6',
                    'type' => 'imagetotext',
                    'module' => 'common',
                    'casesensitive' => false,
                    'img' => $base64
                )
            ]);
            return json_decode($res->getBody());
        } catch (\Throwable $th) {
        }
        return false;
    }

    public function convertCaptcha($base64)
    {
        $client = new Client();
        try {
            $res = $client->request('POST', 'http://103.142.26.15:8122/api/captcha/csgt', [
                'json' => array(
                    'api_key' => "34269d96a9369e2fdd64601ac52e1c3c",
                    'base64' => $base64
                )
            ]);
            return json_decode($res->getBody());
        } catch (\Throwable $th) {
        }
        return false;
    }


    private function callApi($headers, $endpoint, $method = 'GET', $data = [])
    {
        try {

            $response = $this->client->request($method, $endpoint, [
                'form_params' => $data,
                'headers' => $headers,
                'verify' => false,
            ]);

            return $response->getBody()->getContents();
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            $response = json_decode($e->getResponse()->getBody()->getContents(), true);
            Log::error($e);
            return false;
        } catch (\GuzzleHttp\Exception\ConnectException $e) {
            Log::error($e);
            return false;
        }
    }
}
