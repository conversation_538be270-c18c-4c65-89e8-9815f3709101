<?php

namespace App\Services;

use DOMDocument;
use DOMXPath;

class ParseHtmlDetailTaxService
{
    public $dom;
    public $xpath;

    public function __construct($htmlContent)
    {
        $this->dom = new DOMDocument();
        @$this->dom->loadHTML($htmlContent);
        $this->xpath = new DOMXPath($this->dom);
    }

    public function getData()
    {
        return [
            'tax_id' => $this->taxID(),
            'name' => $this->name(),
            'alternate_name' => $this->intlName(),
            'short_alternate_name' => $this->shortName(),
            'address' => $this->address(),
            'representative_name' => $this->representativeName(),
            'phone' => $this->phone(),
            'establish_date' => $this->establishDate(),
            'management_agency' => $this->managementAgency(),
            'company_type' => $this->companyType(),
            'status' => $this->status(),
        ];
    }

    public function managementAgency()
    {
        $managementAgency = null;
        // L<PERSON>y cơ quan quản lý
        // Truy vấn để tìm `td` chứa biểu tượng users
        $tdWithIcon = $this->xpath->query('//td[i[contains(@class, "fa-users")]]');

        if ($tdWithIcon->length > 0) {
            // Lấy `td` kế tiếp chứa ngày
            $nextTd = $tdWithIcon->item(0)->nextSibling;
            if ($nextTd && $nextTd->nodeName === "#text") {
                $nextTd = $nextTd->nextSibling;
            }


            // Kiểm tra nếu có tồn tại và chứa `span`
            if ($nextTd && $nextTd->nodeName === "td") {
                $span = $this->xpath->query('.//span', $nextTd);
                if ($span->length > 0) {
                    $managementAgency =  trim($span->item(0)->nodeValue);
                }
            }
        }

        return $managementAgency;
    }
    public function establishDate()
    {
        $establishDate = null;
        // Truy vấn để tìm `td` chứa biểu tượng lịch
        $tdWithIcon = $this->xpath->query('//td[i[contains(@class, "fa-calendar")]]');

        if ($tdWithIcon->length > 0) {
            // Lấy `td` kế tiếp chứa ngày
            $nextTd = $tdWithIcon->item(0)->nextSibling;

            // Kiểm tra nếu có tồn tại và chứa `span`
            if ($nextTd && $nextTd->nodeName === "td") {
                $span = $this->xpath->query('.//span', $nextTd);
                if ($span->length > 0) {
                    $establishDate =  trim($span->item(0)->nodeValue);
                }
            }
        }

        return $establishDate;
    }
    public function companyType()
    {
        $companyType = $this->xpath->query("//td/a[contains(@href, '/tra-cuu-ma-so-thue-theo-loai-hinh-doanh-nghiep')]")
            ->item(0)->nodeValue ?? '';

        return $companyType;
    }
    public function status()
    {
        $status = $this->xpath->query("//td/a[contains(@href, '/tra-cuu-ma-so-thue-theo-trang-thai-cong-ty')]")->item(0)->nodeValue ?? '';

        return $status;
    }

    public function industry()
    {
        $industry = $this->xpath->query("//td/a[contains(@href, '/tra-cuu-ma-so-thue-theo-nganh-nghe')]")->item(0)->nodeValue ?? '';

        return $industry;
    }

    public function phone()
    {
        $stringPhone = $this->xpath->query("//td[@itemprop='telephone']")->item(0)->nodeValue ?? '';
        $phone = null;
        if (str_contains($stringPhone, 'Ẩn thông tin')) {
            $phone = str_replace('Ẩn thông tin', '', $stringPhone);
        }

        // preg_match('/\d{8,11}(-\s*\d{8,11})?/', $stringPhone, $matches);
        // $phone = !empty($matches) ? $matches[0] : null;

        return trim($phone);
    }
    public function representativeName()
    {
        $representative = $this->xpath->query("//td/span[@itemprop='name']/a")->item(0)->nodeValue ?? '';

        return $representative;
    }

    public function address()
    {
        $address = $this->xpath->query("//td[@itemprop='address']/span")->item(0)->nodeValue ?? '';

        return $address;
    }

    public function taxID()
    {
        $taxID = $this->xpath->query("//td[@itemprop='taxID']/span")->item(0)->nodeValue ?? '';

        return $taxID;
    }

    public function name()
    {
        $name = $this->xpath->query("//th[@itemprop='name']/span")->item(0)->nodeValue ?? '';

        return $name;
    }

    public function intlName()
    {
        $intlName = $this->xpath->query("//td[@itemprop='alternateName']/span")->item(0)->nodeValue ?? '';

        return $intlName;
    }

    public function shortName()
    {
        $shortName = $this->xpath->query("//td[@itemprop='alternateName']/span")->item(1)->nodeValue ?? '';

        return $shortName;
    }

    public function industries()
    {
        $allNode = $this->xpath->query("//tr/td/a[contains(@href, '/tra-cuu-ma-so-thue-theo-nganh-nghe')]");


        $data = [];
        if ($allNode->length > 0) {

            // Lấy thông tin ngành nghề chính
            $nganhNgheNode = $allNode->item(0);
            $url = $nganhNgheNode ? $nganhNgheNode->getAttribute('href') : '';

            // Trích xuất số ở cuối URL bằng regex
            preg_match('/-(\d+)$/', $url, $matches);
            $primaryCode = $matches[1] ?? null;
            $data[] = [
                'code' => $primaryCode,
                'primary' => true,
            ];

            for ($i = 1; $i < $allNode->length; $i += 2) {
                $nganhNgheNode = $allNode->item($i);
                $url = $nganhNgheNode ? $nganhNgheNode->getAttribute('href') : '';

                // Trích xuất số ở cuối URL bằng regex
                preg_match('/-(\d+)$/', $url, $matches);
                $code = $matches[1] ?? null;
                if ($code && $code != $primaryCode) {
                    $data[] = [
                        'code' => $code,
                        'primary' => false,
                    ];
                }
            }
        }

        return $data;
    }
}
