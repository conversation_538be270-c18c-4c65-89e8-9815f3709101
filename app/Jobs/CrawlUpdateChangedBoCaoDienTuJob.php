<?php

namespace App\Jobs;

use App\Models\TaxBusiness;
use App\Services\BoCaoDienTuService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class CrawlUpdateChangedBoCaoDienTuJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $service = new BoCaoDienTuService();
        $updateChangedTax = $service->updateChanged();
        if ($updateChangedTax && !empty($updateChangedTax)) {
            $time = now();
            foreach ($updateChangedTax as $tax) {
                $exist = TaxBusiness::where('tax_id', $tax['code'])->first();
                if (!$exist || ($exist && $exist->updated_at <= now()->subDays(5))) {
                    $time = $time->addSeconds(5);
                    GetTaxInfoFromTCTJob::dispatch($tax['code'])->delay($time);
                }
            }
        }
    }
}
