<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use App\Models\AddressConversion;
use App\Models\OldWard;
use App\Models\ProvinceV2;
use App\Models\WardV2;
use App\Services\AddressConversionService;
use Illuminate\Support\Facades\Log;

class ConvertAddressJob implements ShouldQueue
{
    use Queueable;

    protected $provinceCode;

    /**
     * Create a new job instance.
     */
    public function __construct($provinceCode)
    {
        $this->provinceCode = $provinceCode;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $oldWards = OldWard::whereHas('district', function ($query) {
            $query->whereHas('province', function ($query) {
                $query->where('code', $this->provinceCode);
            });
        })->get();
        foreach ($oldWards as $oldWard) {
            $getNewWard = null;
            // dd($oldWard->name, $oldWard->district->name, $oldWard->district->province->name);
            $service = new AddressConversionService();
            $searchResults = $service->searchInDvhc($oldWard->full_name, 'ward');

            if (!empty($searchResults)) {
                if (count($searchResults) == 1) {
                    $getNewWard = WardV2::where('name', 'like', '%' . $searchResults[0]['new_ward'] . '%')
                        ->whereHas('province', function ($query) use ($searchResults) {
                            $query->where('name', 'like', '%' . $searchResults[0]['new_province'] . '%');
                        })->first();
                    if ($getNewWard && str_contains($searchResults[0]['merged_from'], $oldWard->district->province->name)) {
                        AddressConversion::create([
                            'ward_name' => $oldWard->full_name,
                            'district_name' => $oldWard->district->full_name,
                            'province_name' => $oldWard->district->province->full_name,
                            'detail_address' => '',
                            'new_detail' => $searchResults[0]['all_old_wards'],
                            'new_ward' => $getNewWard->name,
                            'new_ward_code' => $getNewWard->ward_code,
                            'new_province' => $getNewWard->province->name,
                            'new_province_code' => $getNewWard->province->province_code,
                            'new_full_address' => $getNewWard->name . ', ' . $getNewWard->province->name,
                            'not_sure' => false,
                            'success' => true,
                            'ward_code_missing' => false,
                            'province_code_missing' => false,
                        ]);
                    } else {
                        // $getNewWard = WardV2::where('name', 'like', '%' . $searchResults[0]['new_ward'] . '%')
                        //     ->whereHas('province', function ($query) use ($searchResults) {
                        //         $query->where('name', 'like', '%' . $searchResults[0]['new_province'] . '%');
                        //     })->first();
                        Log::debug($searchResults);
                    }
                } else {
                    foreach ($searchResults as $searchResult) {
                        if (
                            str_contains($oldWard->district->province->full_name, $searchResult['new_province'])
                            || str_contains($searchResult['new_province'], $oldWard->district->province->full_name)
                        ) {
                            $getNewWard = WardV2::where('name', 'like', '%' . $searchResult['new_ward'] . '%')
                                ->whereHas('province', function ($query) use ($searchResult) {
                                    $query->where('name', 'like', '%' . $searchResult['new_province'] . '%');
                                })->first();
                            if ($getNewWard && str_contains($searchResults['merged_from'], $oldWard->district->province->name)) {
                                AddressConversion::create([
                                    'ward_name' => $oldWard->full_name,
                                    'district_name' => $oldWard->district->full_name,
                                    'province_name' => $oldWard->district->province->full_name,
                                    'detail_address' => '',
                                    'new_detail' => $searchResult['all_old_wards'],
                                    'new_ward' => $getNewWard->name,
                                    'new_ward_code' => $getNewWard->ward_code,
                                    'new_province' => $getNewWard->province->name,
                                    'new_province_code' => $getNewWard->province->province_code,
                                    'new_full_address' => $getNewWard->name . ', ' . $getNewWard->province->name,
                                    'not_sure' => false,
                                    'success' => true,
                                    'ward_code_missing' => false,
                                    'province_code_missing' => false,
                                ]);
                            }
                        }
                    }
                }
            }
        }
    }
}
