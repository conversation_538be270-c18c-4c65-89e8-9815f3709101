<?php

namespace App\Jobs;

use App\Models\TaxBusiness;
use App\Models\Ward;
use App\Services\MaSoThueService;
use DOMDocument;
use DOMXPath;
use GuzzleHttp\Client;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CrawlByWardJob implements ShouldQueue
{
    use Queueable;

    protected $ward;

    /**
     * Create a new job instance.
     */
    public function __construct($ward)
    {
        $this->ward = $ward;
        $this->onQueue('dev_thongtin_queue');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if (Cache::get('jobs_paused', false)) {
            Log::info('Jobs are paused, retrying later...');
            self::dispatch($this->ward)->delay(now()->addMinutes(5)); // Retry later
            return;
        }
        Log::info('Crawling ward: ' . $this->ward['name']);
        // $getWard = Ward::where('full_name', $this->ward['name'])->first();
        // if (!$getWard) {
        //     Log::info('End ward not exist DB: ' . $this->ward['name']);
        //     exit;
        // }

        $newTaxs = [];
        $currentPage = 1;
        do {
            Log::info('Crawling ward: ' . $this->ward['name'] . ' - Page: ' . $currentPage);
            $result = $this->getDataByPage($currentPage);
            if (isset($result['data'])) {
                $newTaxs = [
                    ...$newTaxs,
                    ...$result['data']
                ];
            }
            $currentPage++;
        } while (isset($result['data']) && !empty($result['data']));

        if ($newTaxs) {
            $time = now();
            foreach ($newTaxs as $tax) {
                $exist = TaxBusiness::where('tax_id', $tax['tax_code'])->first();
                if (!$exist) {
                    $time = $time->addSeconds(5);
                    // CrawlDetailTaxBusinessJob::dispatch($tax, $this->ward)->delay($time);
                    UpdateTaxInfoFromTCTJob::dispatch($tax['tax_code'], $this->ward)->delay($time);
                }
            }
        }
    }

    protected function getDataByPage($page = 1)
    {
        $mstService = new MaSoThueService();
        $result = $mstService->call($this->ward['url'], 'GET', $page);

        if (!$result) {
            self::dispatch($this->ward)->delay(now()->addMinutes(5)); // Retry later
            return;
        }
        $dom = new DOMDocument();
        $htmlContent = $result;
        @$dom->loadHTML($htmlContent);
        $xpath = new DOMXPath($dom);

        // Tìm tất cả các <div> chứa dữ liệu công ty
        $companies = $xpath->query("//div[@class='tax-listing']/div[@data-prefetch]");

        $tax_list = [];
        foreach ($companies as $company) {
            // Lấy tên công ty
            $nameNode = $xpath->query(".//h3/a", $company)->item(0);
            $name = $nameNode ? trim($nameNode->nodeValue) : '';

            // Lấy URL chi tiết
            $url = $nameNode ? $nameNode->getAttribute('href') : '';

            // Lấy mã số thuế
            $taxCodeNode = $xpath->query(".//i[@class='fa fa-hashtag']/following-sibling::a", $company)->item(0);
            $taxCode = $taxCodeNode ? trim($taxCodeNode->nodeValue) : '';

            // Lấy người đại diện
            $representativeNode = $xpath->query(".//i[@class='fa fa-user']/following-sibling::em/a", $company)->item(0);
            $representative = $representativeNode ? trim($representativeNode->nodeValue) : '';

            // Lấy địa chỉ
            $addressNode = $xpath->query(".//address", $company)->item(0);
            $address = $addressNode ? trim($addressNode->nodeValue) : '';

            // Thêm vào danh sách kết quả
            $tax_list[] = [
                'name' => $name,
                'url' => $url,
                'tax_code' => $taxCode,
                'representative' => $representative,
                'address' => $address
            ];
        }

        // Lấy số trang hiện tại
        $currentPageNode = $xpath->query("//span[contains(@class, 'current')]");
        $currentPage = $currentPageNode->length > 0 ? $currentPageNode->item(0)->nodeValue : null;

        // Lấy danh sách các trang tiếp theo
        $nextPages = [];
        $pageNodes = $xpath->query("//a[contains(@class, 'page-numbers')]");

        foreach ($pageNodes as $node) {
            $nextPages[] = [
                'page' => $node->nodeValue,
                'url' => $node->getAttribute('href')
            ];
        }

        if ($page > $currentPage && $currentPage == 1) {
            $tax_list = null;
        }

        return [
            'data' => $tax_list,
        ];
    }
}
