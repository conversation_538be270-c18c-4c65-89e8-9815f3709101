<?php

namespace App\Jobs;

use App\Models\AddressConversion;
use App\Models\Province;
use App\Models\Ward;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ConvertOldWardToNewWardJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $dvhcData;
    private $batchSize;
    private $offset;

    /**
     * Create a new job instance.
     */
    public function __construct($batchSize = 100, $offset = 0)
    {
        $this->batchSize = $batchSize;
        $this->offset = $offset;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info("Starting ConvertOldWardToNewWardJob - Batch size: {$this->batchSize}, Offset: {$this->offset}");

            // Load DVHC data
            $this->loadDvhcData();

            // Process AddressConversion records in batches
            $addressConversions = AddressConversion::skip($this->offset)
                ->take($this->batchSize)
                ->get();

            $stats = [
                'processed' => 0,
                'updated' => 0,
                'not_found' => 0,
                'errors' => 0
            ];

            foreach ($addressConversions as $conversion) {
                $stats['processed']++;
                
                try {
                    $result = $this->updateConversionRecord($conversion);
                    
                    if ($result['updated']) {
                        $stats['updated']++;
                    } else {
                        $stats['not_found']++;
                    }
                } catch (\Exception $e) {
                    $stats['errors']++;
                    Log::error("Error processing conversion ID {$conversion->id}: " . $e->getMessage());
                }
            }

            Log::info("ConvertOldWardToNewWardJob completed", $stats);

        } catch (\Exception $e) {
            Log::error("ConvertOldWardToNewWardJob failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Load DVHC data from JSON file
     */
    private function loadDvhcData()
    {
        $dvhcPath = database_path('data/dvhc.json');
        
        if (!file_exists($dvhcPath)) {
            throw new \Exception('DVHC JSON file not found at: ' . $dvhcPath);
        }

        $jsonContent = file_get_contents($dvhcPath);
        $this->dvhcData = json_decode($jsonContent, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception('Invalid JSON in DVHC file: ' . json_last_error_msg());
        }
    }

    /**
     * Update a single AddressConversion record
     */
    private function updateConversionRecord(AddressConversion $conversion)
    {
        $oldWardName = $this->convertToUtf8($conversion->ward_name);
        
        // Search for the old ward in DVHC data
        $dvhcMatch = $this->findOldWardInDvhc($oldWardName);
        
        if (!$dvhcMatch) {
            return ['updated' => false, 'reason' => 'not_found_in_dvhc'];
        }

        // Extract new information from DVHC match
        $newProvinceName = $dvhcMatch['new_province'];
        $newWardName = $dvhcMatch['new_ward'];
        
        // Find current administrative units in database
        $currentProvince = $this->findCurrentProvince($newProvinceName);
        $currentWard = $this->findCurrentWard($newWardName);

        // Update the conversion record
        $updateData = [
            'new_province' => $currentProvince ? $currentProvince->full_name : $newProvinceName,
            'new_province_code' => $currentProvince ? $currentProvince->code : null,
            'new_ward' => $currentWard ? $currentWard->full_name : $newWardName,
            'new_ward_code' => $currentWard ? $currentWard->code : null,
            'new_detail' => $currentWard ? $currentWard->full_name : $newWardName,
            'new_full_address' => $this->buildNewFullAddress($currentWard, $currentProvince),
            'success' => $currentProvince && $currentWard,
            'not_sure' => !$currentWard,
            'ward_code_missing' => !$currentWard,
            'province_code_missing' => !$currentProvince,
        ];

        $conversion->update($updateData);

        return [
            'updated' => true, 
            'found_province' => !!$currentProvince,
            'found_ward' => !!$currentWard
        ];
    }

    /**
     * Find old ward in DVHC data
     */
    private function findOldWardInDvhc($oldWardName)
    {
        foreach ($this->dvhcData['rows'] as $row) {
            $columns = $row['columns'];
            
            // Structure: [new_province, merged_from, new_ward, old_wards]
            $newProvince = $columns[0] ?? '';
            $mergedFrom = $columns[1] ?? '';
            $newWard = $columns[2] ?? '';
            $oldWards = $columns[3] ?? '';

            // Parse old ward names (comma separated)
            $oldWardList = array_map('trim', explode(',', $oldWards));

            foreach ($oldWardList as $oldWard) {
                if ($this->isMatchingWard($oldWardName, $oldWard)) {
                    return [
                        'new_province' => $newProvince,
                        'merged_from' => $mergedFrom,
                        'new_ward' => $newWard,
                        'old_ward' => $oldWard,
                        'all_old_wards' => $oldWards
                    ];
                }
            }
        }

        return null;
    }

    /**
     * Check if two ward names match
     */
    private function isMatchingWard($wardName1, $wardName2)
    {
        $ward1 = $this->normalizeWardName($wardName1);
        $ward2 = $this->normalizeWardName($wardName2);

        // Exact match
        if ($ward1 === $ward2) {
            return true;
        }

        // Contains match
        if (mb_strpos($ward1, $ward2, 0, 'UTF-8') !== false || 
            mb_strpos($ward2, $ward1, 0, 'UTF-8') !== false) {
            return true;
        }

        // Similarity match (80% threshold)
        similar_text($ward1, $ward2, $percent);
        return ($percent / 100) >= 0.8;
    }

    /**
     * Normalize ward name for comparison
     */
    private function normalizeWardName($wardName)
    {
        $wardName = $this->convertToUtf8($wardName);
        $wardName = mb_strtolower($wardName, 'UTF-8');
        
        // Remove common prefixes
        $prefixes = ['xã ', 'thị trấn ', 'phường ', 'thị xã ', 'thành phố '];
        
        foreach ($prefixes as $prefix) {
            if (mb_strpos($wardName, $prefix, 0, 'UTF-8') === 0) {
                $wardName = mb_substr($wardName, mb_strlen($prefix, 'UTF-8'), null, 'UTF-8');
                break;
            }
        }
        
        return trim($wardName);
    }

    /**
     * Find current province in database
     */
    private function findCurrentProvince($provinceName)
    {
        // Direct match
        $province = Province::where('full_name', $provinceName)->first();
        
        if (!$province) {
            // Fuzzy match
            $province = Province::where('full_name', 'like', '%' . $provinceName . '%')->first();
        }
        
        return $province;
    }

    /**
     * Find current ward in database
     */
    private function findCurrentWard($wardName)
    {
        // Direct match
        $ward = Ward::where('full_name', $wardName)->first();
        
        if (!$ward) {
            // Fuzzy match
            $ward = Ward::where('full_name', 'like', '%' . $wardName . '%')->first();
        }
        
        return $ward;
    }

    /**
     * Build new full address
     */
    private function buildNewFullAddress($ward, $province)
    {
        $parts = [];
        
        if ($ward) {
            $parts[] = $ward->full_name;
            
            if ($ward->district) {
                $parts[] = $ward->district->full_name;
            }
        }
        
        if ($province) {
            $parts[] = $province->full_name;
        }
        
        return implode(', ', $parts);
    }

    /**
     * Convert string to UTF-8
     */
    private function convertToUtf8($text)
    {
        if (empty($text)) {
            return $text;
        }

        if (mb_check_encoding($text, 'UTF-8')) {
            return $text;
        }

        $encoding = mb_detect_encoding($text, ['UTF-8', 'ISO-8859-1', 'Windows-1252', 'ASCII'], true);
        
        if ($encoding && $encoding !== 'UTF-8') {
            $converted = mb_convert_encoding($text, 'UTF-8', $encoding);
            return $converted !== false ? $converted : $text;
        }

        return mb_convert_encoding($text, 'UTF-8', 'UTF-8');
    }
}
