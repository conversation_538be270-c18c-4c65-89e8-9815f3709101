<?php

namespace App\Jobs;

use App\Models\OldWard;
use App\Models\WardMapping;
use App\Models\WardV2;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class WardMappingJob implements ShouldQueue
{
    use Queueable;

    protected $row;

    /**
     * Create a new job instance.
     */
    public function __construct($row)
    {
        $this->row = $row;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $oldWardCodes = explode(";", $this->row[6]);
        $newWardCode = $this->row[0];
        $newWard = WardV2::where('ward_code', $newWardCode)->first();
        if ($newWard) {
            foreach ($oldWardCodes as $oldWardCode) {
                $oldWard = OldWard::where('code', $oldWardCode)->first();
                WardMapping::create([
                    'old_ward_code' => $oldWardCode,
                    'old_ward_name' => $oldWard?->full_name,
                    'old_district_name' => $oldWard?->district?->full_name,
                    'old_province_name' => $oldWard?->district?->province?->full_name,
                    'new_ward_code' => $newWardCode,
                    'new_ward_name' => $newWard?->name,
                    'new_province_name' => $newWard?->province?->name,
                ]);
            }
        } else {
            Log::debug($this->row);
        }
    }
}
