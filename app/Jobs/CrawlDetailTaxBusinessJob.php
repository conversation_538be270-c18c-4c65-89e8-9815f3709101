<?php

namespace App\Jobs;

use App\Models\TaxBusiness;
use App\Models\TaxIndustry;
use App\Services\MaSoThueService;
use App\Services\ParseHtmlDetailTaxService;
use DOMDocument;
use DOMXPath;
use GuzzleHttp\Client;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CrawlDetailTaxBusinessJob implements ShouldQueue
{
    use Queueable;

    protected $tax;
    protected $getWard;

    /**
     * Create a new job instance.
     */
    public function __construct($tax, $getWard)
    {
        $this->tax = $tax;
        $this->getWard = $getWard;
        $this->onQueue('dev_thongtin_queue');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if (Cache::get('jobs_paused', false)) {
            Log::info('Jobs are paused, retrying later...');
            self::dispatch($this->tax, $this->getWard)->delay(now()->addMinutes(5)); // Retry later
            return;
        }
        $mstService = new MaSoThueService();
        $result = $mstService->call($this->tax['url']);
        if (!$result) {
            self::dispatch($this->tax, $this->getWard)->delay(now()->addMinutes(5)); // Retry later
            return;
        }
        Log::info('Crawling detail tax: ' . $this->tax['url']);

        $htmlContent = $result;
        $dom = new DOMDocument();
        @$dom->loadHTML($htmlContent);

        $parseHtmlDetailTaxService = new ParseHtmlDetailTaxService($htmlContent);
        $dataTaxBusiness = $parseHtmlDetailTaxService->getData();
        $dataTaxBusiness = [
            ...$dataTaxBusiness,
            'ward_code' => $this->getWard['code'],
        ];

        $industries = $parseHtmlDetailTaxService->industries();

        $industry_strings = '';
        $primary = null;
        if ($industries) {
            foreach ($industries as $industry) {
                $industry_strings .= $industry['code'] . ', ';
                if ($industry['primary'] == 'Y'){
                    $primary = $industry['code'];
                }
            }
            $dataTaxBusiness['industries'] = $industry_strings;
            $dataTaxBusiness['primary_industry'] = $primary;
        }
        $tax_business = TaxBusiness::create($dataTaxBusiness);

//        if ($industries) {
//            $newIndustries = [];
//            foreach ($industries as $industry) {
//                $newIndustries[] = [
//                    'tax_business_id' => $tax_business->id,
//                    'industry_code' => $industry['code'],
//                    'primary' => $industry['primary'],
//                ];
//            }
//            TaxIndustry::insert($newIndustries);
//        }
    }
}
