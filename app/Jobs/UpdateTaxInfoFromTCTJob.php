<?php

namespace App\Jobs;

use App\Models\TaxBusiness;
use App\Models\TaxIndustry;
use App\Services\CrawlTaxInfoFromTCTService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use Telegram\Bot\Laravel\Facades\Telegram;

class UpdateTaxInfoFromTCTJob implements ShouldQueue
{
    use Queueable;


    protected $tax_id;
    protected $ward;
    /**
     * Create a new job instance.
     */
    public function __construct($tax_id, $ward = [])
    {
        $this->tax_id = $tax_id;
        $this->ward = $ward;
        $this->onQueue('dev_thongtin_queue');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $service = new CrawlTaxInfoFromTCTService();
        $info = $service->handle($this->tax_id);
        if (isset($info['status']) && $info['status'] == 'notfound') {
            Log::info('Done : NotFound - MST: ' . $this->tax_id);
            return;
        }
        if (isset($info['captcha']) && !$info['captcha']) {
            Log::warning('Fail Captcha -> Retry : UpdateTaxInfoFromTCTJob - MST: ' . $this->tax_id);
            self::dispatch($this->tax_id, $this->ward)->delay(now()->addSeconds(5)); // Retry later
            return;
        }

        if ($info && isset($info['taxid'])) {
            $dataTax = [
                'tax_id' => $info['taxid'],
                'name' => $info['official_name'],
                'alternate_name' => $info['official_name'],
                'short_alternate_name' => $info['trading_name'],
                'address' => $info['head_office_address'],
                'representative_name' => $info['legal_representative'],
                'id_number' => $info['legal_representative_id'],
                'phone' => $info['phone'],
                'establish_date' => now()->parse(str_replace("/", "-", $info['start_date']))->format('Y-m-d'),
                'management_agency' => $info['tax_management_office'],
                'company_type' => null,
                'status' => $info['note'],
            ];

            if ($info['industries'] && !empty($info['industries'])) {
                $industries = '';
                $primary_industries = null;
                foreach ($info['industries'] as $industry) {
                    $industries .= $industry['code'] . ',';
                    if ($industry['primary'] == 'Y') {
                        $primary_industries = $industry['code'];
                    }
                }
                $dataTax['industries'] = $industries;
                $dataTax['primary_industry'] = $primary_industries;
            }

            $newTax = TaxBusiness::where('tax_id', $info['taxid'])->first();
            if ($newTax) {
                $newTax->update($dataTax);
                return;
            } else {
                $dataTax['ward_code'] = $this->ward['code'];
                $newTax = TaxBusiness::create($dataTax);
            }
            // $telegram = Telegram::bot('mybot');
            // $replyText = "Doanh nghiệp vừa được cập nhật.\n\n";
            // $replyText .= "✅ <strong>Mã số thuế:</strong> {$newTax->tax_id}\n";
            // $replyText .= "✅ <strong>Tên doanh nghiệp:</strong> {$newTax->name}\n";
            // $replyText .= "✅ <strong>Người đại diện:</strong> {$newTax->representative_name}\n";
            // $replyText .= "✅ <strong>Địa chỉ:</strong> {$newTax->address}\n";
            // $replyText .= "✅ <strong>Ngày hoạt động:</strong> {$newTax->establish_date}\n";
            // $replyText .= "✅ <strong>Quản lý bởi:</strong> {$newTax->management_agency}\n";
            // $replyText .= "✅ <strong>Tình trạng:</strong> {$newTax->status}";
            // $telegram->sendMessage([
            //     'parse_mode' => 'HTML',
            //     'chat_id' => '-1002366424970',
            //     'text' => $replyText,
            // ]);
            Log::info('Done : UpdateTaxInfoFromTCTJob - MST: ' . $info['taxid']);
        } else {
            Log::warning('Retry : UpdateTaxInfoFromTCTJob - MST: ' . $this->tax_id);
            self::dispatch($this->tax_id, $this->ward)->delay(now()->addSeconds(5)); // Retry later
            return;
        }
    }
}
