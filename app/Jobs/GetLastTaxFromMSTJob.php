<?php

namespace App\Jobs;

use App\Models\TaxBusiness;
use App\Models\Ward;
use App\Models\WardV2;
use App\Services\CrawlTaxInfoFromTCTService;
use App\Services\MaSoThueService;
use DOMDocument;
use DOMXPath;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class GetLastTaxFromMSTJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $mstService = new MaSoThueService();
        $crawlTaxInfoFromTCTService = new CrawlTaxInfoFromTCTService();
        $result = $mstService->call('');
        $dom = new DOMDocument();
        $htmlContent = $result;
        @$dom->loadHTML($htmlContent);
        $xpath = new DOMXPath($dom);
        $companies = $xpath->query("//section[@class='tax-listing']/div");
        $tax_list = [];
        foreach ($companies as $company) {
            // Lấy tên công ty
            $nameNode = $xpath->query(".//h3/a", $company)->item(0);
            $name = $nameNode ? trim($nameNode->nodeValue) : '';

            // Lấy URL chi tiết
            $url = $nameNode ? $nameNode->getAttribute('href') : '';

            // Lấy mã số thuế
            $taxCodeNode = $xpath->query(".//i[@class='fa fa-hashtag']/following-sibling::a", $company)->item(0);
            $taxCode = $taxCodeNode ? trim($taxCodeNode->nodeValue) : '';

            // Lấy người đại diện
            $representativeNode = $xpath->query(".//i[@class='fa fa-user']/following-sibling::em/a", $company)->item(0);
            $representative = $representativeNode ? trim($representativeNode->nodeValue) : '';

            // Lấy địa chỉ
            $addressNode = $xpath->query(".//address", $company)->item(0);
            $address = $addressNode ? trim($addressNode->nodeValue) : '';

            // Thêm vào danh sách kết quả
            $tax_list[] = [
                'name' => $name,
                'url' => $url,
                'tax_code' => $taxCode,
                'representative' => $representative,
                'address' => $address
            ];
        }
        $tax_list = array_reverse($tax_list);

        foreach ($tax_list as $key => $tax) {
            $exist = TaxBusiness::where('tax_id', $tax['tax_code'])->first();
            if (!$exist) {
                $info = $crawlTaxInfoFromTCTService->handle($tax['tax_code']);
                if ($info && isset($info['taxid'])) {
                    $dataTax = [
                        'tax_id' => $info['taxid'],
                        'name' => $info['official_name'],
                        'alternate_name' => $info['official_name'],
                        'short_alternate_name' => $info['trading_name'],
                        'address' => $info['head_office_address'],
                        'representative_name' => $info['legal_representative'],
                        'id_number' => $info['legal_representative_id'],
                        'phone' => $info['phone'],
                        'establish_date' => now()->parse(str_replace("/", "-", $info['start_date']))->format('Y-m-d'),
                        'management_agency' => $info['tax_management_office'],
                        'company_type' => null,
                        'status' => $info['note'],
                        'director_name' => $info['director_name'] ?? null,
                        'chief_accountant' => $info['chief_accountant'] ?? null,
                    ];

                    if ($info['industries'] && !empty($info['industries'])) {
                        $industries = '';
                        $primary_industries = null;
                        foreach ($info['industries'] as $industry) {
                            $industries .= $industry['code'] . ',';
                            if ($industry['primary'] == 'Y') {
                                $primary_industries = $industry['code'];
                            }
                        }
                        $dataTax['industries'] = $industries;
                        $dataTax['primary_industry'] = $primary_industries;
                    }


                    $addressSplit = explode(',', $dataTax['address']);
                    $provinceName = $addressSplit[count($addressSplit) - 1] ?? null;
                    $wardName = $addressSplit[count($addressSplit) - 3] ?? null;
                    if ($wardName) {
                        $formatWardName = preg_replace_callback('/\d+/', function ($matches) {
                            return str_pad($matches[0], 2, '0', STR_PAD_LEFT);
                        }, $wardName);
                        $ward = WardV2::where(function ($query) use ($formatWardName, $wardName) {
                            $query->where('name', 'like', '%' . trim($wardName) . '%')
                                ->orWhere('name', 'like', '%' . trim($formatWardName) . '%');
                        })
                            ->whereHas('province', function ($query) use ($provinceName) {
                                $query->where('name', 'like', '%' . trim(str_replace("TP", "", $provinceName)) . '%');
                            })->first();
                        if ($ward) {
                            $dataTax['ward_code'] = $ward->ward_code;
                        }
                    }
                    $newTax = TaxBusiness::create($dataTax);
                }
            }
        }
    }
}
