<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TaxBusinessResource\Pages;
use App\Filament\Resources\TaxBusinessResource\RelationManagers;
use App\Models\TaxBusiness;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;

class TaxBusinessResource extends Resource
{
    protected static ?string $model = TaxBusiness::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $label = 'Thuế doanh nghiệp';

    public static function canCreate(): bool
    {
        return false;
    }


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('tax_id')
                    ->label('Mã số thuế')
                    ->searchable()
                    ->url(fn (TaxBusiness $record): string => route('detail.business', ['taxSlug' => $record->tax_id . '-' . Str::slug($record->name)]))
                    ->openUrlInNewTab(),
                TextColumn::make('name')->label('Tên doanh nghiệp')->searchable(),
                TextColumn::make('representative_name')->label('Đại diện'),
                TextColumn::make('phone')->label('Số điện thoại')->searchable()->copyable(),
                TextColumn::make('establish_date')->label('Ngày hoạt động')
                    ->formatStateUsing(fn ($state) => now()->parse($state)->format('d/m/Y'))
            ])
            ->filters([
                Filter::make('phone')->label('Có số điện thoại')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('phone')),
                Filter::make('establish_date')
                    ->form([
                        DatePicker::make('picker_date')->label('Ngày hoạt động')->native(false),
                    ])
                    ->query(function ($query, array $data) {
                        return $query
                            ->when(
                                $data['picker_date'],
                                fn ($q, $date) =>
                                $q->whereDate('establish_date', $date)
                            );
                    })
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTaxBusinesses::route('/'),
            // 'create' => Pages\CreateTaxBusiness::route('/create'),
            // 'edit' => Pages\EditTaxBusiness::route('/{record}/edit'),
        ];
    }
}
