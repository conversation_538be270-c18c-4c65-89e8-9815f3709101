<?php

namespace App\Filament\Resources\TaxBusinessResource\Pages;

use App\Filament\Resources\TaxBusinessResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTaxBusinesses extends ListRecords
{
    protected static string $resource = TaxBusinessResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
