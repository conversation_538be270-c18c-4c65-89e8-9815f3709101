# Address Conversion System

This system allows you to search for old administrative unit names (ward, district, province) in the DVHC (Đơn vị hành chính) JSON data and create new AddressConversion records.

## Features

- Search old administrative unit names in DVHC data
- Automatically match with new administrative units
- Create AddressConversion records for data migration
- Web interface and command-line tools
- Statistics and reporting

## Components

### 1. Models

- **AddressConversion**: Stores conversion mappings between old and new administrative units
- **Province, District, Ward**: Current administrative units
- **OldProvince, OldDistrict, OldWard**: Historical administrative units (if available)

### 2. Services

- **AddressConversionService**: Core service for searching DVHC data and creating conversions

### 3. Commands

- **ProcessDvhcDataCommand**: Command-line tool for processing DVHC data
- **TestAddressConversionCommand**: Test command to verify functionality

### 4. Web Interface

- **AddressConversionTool**: Livewire component for web-based address conversion
- Available at: `/cong-cu-chuyen-doi-dia-chi`

## Usage

### Command Line

#### Show DVHC Statistics
```bash
php artisan dvhc:process --stats
```

#### Search for Administrative Units
```bash
# Search all types
php artisan dvhc:process "An Giang"

# Search specific type
php artisan dvhc:process "Châu Thành" --type=district
php artisan dvhc:process "Thị trấn" --type=ward
```

#### Create AddressConversion Records
```bash
# Search and create records
php artisan dvhc:process "An Giang" --create
```

#### Test the System
```bash
php artisan test:address-conversion
```

### Web Interface

1. Navigate to `/cong-cu-chuyen-doi-dia-chi`
2. Enter search term (old province, district, or ward name)
3. Select search type (optional)
4. Click "Tìm kiếm" to search
5. Click "Tạo bản ghi chuyển đổi" to create AddressConversion records

## DVHC Data Format

The system expects a JSON file at `database/data/dvhc.json` with the following structure:

```json
{
    "rows": [
        {
            "columns": [
                "Province Name",
                "District Name", 
                "Ward Names (comma separated)"
            ]
        }
    ]
}
```

Example:
```json
{
    "rows": [
        {
            "columns": [
                "An Giang",
                "An Biên",
                "Thị trấn Thứ Ba, Xã Đông Yên, Xã Hưng Yên"
            ]
        }
    ]
}
```

## AddressConversion Table Structure

```sql
CREATE TABLE address_conversions (
    id BIGINT PRIMARY KEY,
    ward_name VARCHAR(255),           -- Old ward name
    district_name VARCHAR(255),       -- Old district name  
    province_name VARCHAR(255),       -- Old province name
    detail_address TEXT,              -- Original search term
    new_detail TEXT,                  -- New detail information
    new_ward VARCHAR(255),            -- New ward name
    new_ward_code VARCHAR(255),       -- New ward code
    new_province VARCHAR(255),        -- New province name
    new_province_code VARCHAR(255),   -- New province code
    new_full_address TEXT,            -- Complete new address
    not_sure BOOLEAN DEFAULT FALSE,   -- Uncertain conversion
    success BOOLEAN DEFAULT FALSE,    -- Successful conversion
    ward_code_missing BOOLEAN DEFAULT FALSE,
    province_code_missing BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

## API Methods

### AddressConversionService

#### `searchInDvhc($searchTerm, $type = 'all')`
Search for administrative units in DVHC data.

**Parameters:**
- `$searchTerm`: Search keyword
- `$type`: 'all', 'province', 'district', or 'ward'

**Returns:** Array of search results

#### `createAddressConversions($searchResults, $searchTerm)`
Create AddressConversion records from search results.

**Parameters:**
- `$searchResults`: Results from searchInDvhc()
- `$searchTerm`: Original search term

**Returns:** Array with creation statistics

#### `getStatistics()`
Get DVHC data statistics.

**Returns:** Array with statistics

## Examples

### Search for Province
```php
$service = new AddressConversionService();
$results = $service->searchInDvhc('An Giang', 'province');
```

### Create Conversions
```php
$service = new AddressConversionService();
$results = $service->searchInDvhc('An Giang', 'province');
$conversions = $service->createAddressConversions($results, 'An Giang');
```

### Get Statistics
```php
$service = new AddressConversionService();
$stats = $service->getStatistics();
echo "Total rows: " . $stats['total_rows'];
```

## Error Handling

The system includes comprehensive error handling:

- Invalid JSON format detection
- Missing DVHC file handling
- Database constraint violations
- Search result validation

## Performance Considerations

- DVHC data is loaded once per service instance
- Large datasets may require memory optimization
- Consider indexing AddressConversion table for frequent queries
- Batch processing for large conversion operations

## Troubleshooting

### Common Issues

1. **DVHC file not found**
   - Ensure `database/data/dvhc.json` exists
   - Check file permissions

2. **Invalid JSON format**
   - Validate JSON syntax
   - Check encoding (UTF-8 recommended)

3. **No search results**
   - Verify search term spelling
   - Try different search types
   - Check DVHC data completeness

4. **Conversion failures**
   - Review current administrative unit data
   - Check Province/District/Ward model relationships
   - Verify database constraints

### Debug Commands

```bash
# Check DVHC file
php artisan dvhc:process --stats

# Test specific search
php artisan dvhc:process "test term" --type=all

# Run full test suite
php artisan test:address-conversion
```
