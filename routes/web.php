<?php

use App\Http\Controllers\HomeController;
use App\Http\Controllers\PageController;
use App\Models\AddressConversion;
use App\Models\OldWard;
use App\Models\ProvinceV2;
use App\Models\WardV2;
use App\Services\AddressConversionService;
use App\Services\BoCaoDienTuService;
use App\Services\CrawlCSGTService;
use App\Services\CrawlMSTDN;
use App\Services\CrawlTaxInfoFromTCTService;
use App\Services\CrawlTaxPersonalFromTCTService;
use App\Services\MaSoThueService;
use App\Services\ParseHtmlDetailTaxService;
use App\Services\RecaptchaV2Proxyless;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\ImageManager;
use Telegram\Bot\Laravel\Facades\Telegram;
use TwoCaptcha\TwoCaptcha;


Route::get('/test-telegram', function () {
    $response = Telegram::bot('mybot');
    $result = $response->setWebhook([
        'url' => route('telegram.webhook')
    ]);
    dd($result);
});

Route::get('/convert', function () {
    $oldWards = OldWard::all();
    foreach ($oldWards as $oldWard) {
        // dd($oldWard->name, $oldWard->district->name, $oldWard->district->province->name);
        $service = new AddressConversionService();
        $searchResults = $service->searchInDvhc('huyện Lệ Thủy', 'ward');
        dd($searchResults);
        if (!empty($searchResults)) {
            if (count($searchResults) == 1) {
                $getNewWard = WardV2::where('name', 'like', '%' . $searchResults[0]['current_district'] . '%')
                    ->whereHas('province', function ($query) use ($searchResults) {
                        $query->where('name', 'like', '%' . $searchResults[0]['current_province'] . '%');
                    })->first();
                // dd($getNewWard, $getNewWard->province->name);
                if ($getNewWard) {
                    AddressConversion::create([
                        'ward_name' => $oldWard->full_name,
                        'district_name' => $oldWard->district->full_name,
                        'province_name' => $oldWard->district->province->full_name,
                        'detail_address' => '',
                        'new_detail' => '',
                        'new_ward' => $getNewWard->name,
                        'new_ward_code' => $getNewWard->ward_code,
                        'new_province' => $getNewWard->province->name,
                        'new_province_code' => $getNewWard->province->province_code,
                        'new_full_address' => $getNewWard->name . ', ' . $getNewWard->province->name,
                        'not_sure' => false,
                        'success' => true,
                        'ward_code_missing' => false,
                        'province_code_missing' => false,
                    ]);
                }
            }
        }
    }
});

Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/search', [HomeController::class, 'search'])->name('search');
Route::get('/tra-cuu-ma-so-thue-ca-nhan', [HomeController::class, 'searchTaxPersonal'])->name('search-tax-personal');
Route::get('/ma-so-thue-ca-nhan/{taxSlug}', [HomeController::class, 'detailTaxPersonal'])->name('detail.personal');
Route::get('/ma-so-thue-doanh-nghiep/{taxSlug}', [HomeController::class, 'detailTaxBusiness'])->name('detail.business');
Route::get('/tra-cuu-ma-so-thue-theo-nganh-nghe', [HomeController::class, 'industries'])->name('industries');
Route::get('/tra-cuu-ma-so-thue-theo-tinh', [HomeController::class, 'provinces'])->name('provinces');
Route::get('/tra-cuu-ma-so-thue-theo-tinh/{provinceCode}', [HomeController::class, 'searchByProvince'])->name('search-by-province');
Route::get('/tra-cuu-ma-so-thue-theo-nganh-nghe/{industryCode}', [HomeController::class, 'searchByIndustry'])->name('search-by-industry');
Route::get('/tra-cuu-phat-nguoi', [HomeController::class, 'phatNguoi'])->name('phat-nguoi');
Route::get('/tra-cuu-phat-nguoi/{bks}', [HomeController::class, 'detailPhatNguoi'])->name('detai.phat-nguoi');
Route::get('/demo-don-vi-hanh-chinh', [HomeController::class, 'administrativeUnitsDemo'])->name('administrative-units-demo');
Route::get('/cong-cu-chuyen-doi-dia-chi', [HomeController::class, 'addressConversionTool'])->name('address-conversion-tool');

Route::get('/danh-ba-website', [HomeController::class, 'index'])->name('danh-ba-website');
Route::get('/{slug}', [PageController::class, 'show'])->name('page.show');

Route::get('/verify-bot-mst', function () {


    $solver = new TwoCaptcha(config('twocaptcha.api_key'));
    $result = $solver->recaptcha([
        'sitekey' => '6LcHY_kZAAAAALb2De4UUonssmRquIktWuABe-W5',
        'url'     => 'https://masothue.com',
    ]);
    if ($result && $result->code) {
        Log::warning('Start Bypass captcha: ' . $result->code);
        $client = new Client();
        $res = $client->request('POST', 'https://masothue.com/Ajax/checkHumanCaptcha', [
            'headers' => [
                'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            ],
            'form_params' => [
                'reCaptcha' => $result->code,
            ]
        ]);
        Cache::forget('jobs_paused');
        Log::warning('Done Bypass captcha -> jobs_paused -> false');
        dd($res->getBody()->getContents());
    }
});
Route::get('/test', function () {
    $service = new CrawlTaxInfoFromTCTService();
    $info = $service->handle('0313585300-001');
    dd($info);
});

Route::get('/test-personal', function () {
    $service = new CrawlTaxPersonalFromTCTService();

    $info = $service->handle('8378888442');
    dd($info);
});

Route::get('/test-phatnguoi', function () {
    $service = new CrawlCSGTService();

    $info = $service->phatnguoi('50f00446');
    dd($info);
});
Route::get('/crawl_captcha', function () {
    $service = new CrawlCSGTService();
    for ($i = 1; $i <= 1000; $i++) {
        $captcha = $service->getCaptcha();
        $result_image  = base64_encode($captcha);

        $resolvedCaptcha = $service->autoCaptchaPro($result_image);

        if ($resolvedCaptcha && $resolvedCaptcha->captcha) {

            Storage::put('captcha/Dataset/' . $resolvedCaptcha->captcha . ".png", $captcha);
        }
    }
});

// Route::get('/test-bo-cao', function () {
//     $service = new BoCaoDienTuService();
//     dd($service->updateChanged());
// });