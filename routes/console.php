<?php

use App\Console\Commands\GenerateSitemap;
use App\Jobs\CrawlNewRegisterBoCaoDienTuJob;
use App\Jobs\CrawlRegisterChangeBoCaoDienTuJob;
use App\Jobs\CrawlUpdateChangedBoCaoDienTuJob;
use App\Jobs\GetLastTaxFromMSTJob;
use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

Schedule::job(new GetLastTaxFromMSTJob())->everyFiveMinutes();
Schedule::job(new CrawlNewRegisterBoCaoDienTuJob())->everyFiveMinutes();
Schedule::job(new CrawlRegisterChangeBoCaoDienTuJob())->everyFiveMinutes();
Schedule::job(new CrawlUpdateChangedBoCaoDienTuJob())->everyFiveMinutes();
Schedule::job(new GenerateSitemap())->daily();
