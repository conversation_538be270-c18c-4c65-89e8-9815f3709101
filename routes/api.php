<?php

use App\Http\Controllers\Api\AddressConversionController;
use App\Http\Controllers\Api\BusinessTaxApiController;
use App\Http\Controllers\Api\TelegramApiController;
use App\Models\TaxBusiness;
use App\Models\Ward;
use Elastic\Elasticsearch\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MatchPhraseQuery;
use Telegram\Bot\Laravel\Facades\Telegram;

Route::post('/botman-webhook', function (Request $request) {
    Log::debug($request->all());
    return response()->json(['status' => true]);
});
Route::get('/botman-webhook', function (Request $request) {
    Log::debug($request->all());
    $data = $request->all();
    return $data['hub_challenge'];
});

Route::post('/telegram/webhook', [TelegramApiController::class, 'webhook'])->name('telegram.webhook');
Route::get('/business-tax/{tax_id}', [BusinessTaxApiController::class, 'get']);
Route::get('/address-conversion', [AddressConversionController::class, 'conversion']);
